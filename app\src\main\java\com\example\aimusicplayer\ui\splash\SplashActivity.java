package com.example.aimusicplayer.ui.splash;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.ui.login.LoginActivity;
import com.example.aimusicplayer.ui.main.MainActivity;
import com.example.aimusicplayer.viewmodel.SplashViewModel;
import com.example.aimusicplayer.utils.PerformanceUtils;

import dagger.hilt.android.AndroidEntryPoint;

/**
 * 欢迎页Activity
 * 使用MVVM架构和Hilt依赖注入
 */
@AndroidEntryPoint
public class SplashActivity extends AppCompatActivity {

    private static final String TAG = "SplashActivity";
    private SplashViewModel viewModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);

            // 初始化ViewModel
            viewModel = new ViewModelProvider(this).get(SplashViewModel.class);

            // 使用现代API设置全屏模式
            PerformanceUtils.setFullscreen(this, true);

            // 设置视图
            setContentView(R.layout.activity_splash);

            // 找到UI元素并添加安全检查
            ImageView logoImageView = findViewById(R.id.splashImageView);
            TextView appNameTextView = findViewById(R.id.app_name_text);
            TextView welcomeTextView = findViewById(R.id.welcome_text);

            // 创建更加平滑的动画
            Animation fadeIn = new AlphaAnimation(0.0f, 1.0f);
            fadeIn.setDuration(1500);

            // 为所有UI元素添加空值检查
            if (logoImageView != null) {
                logoImageView.startAnimation(fadeIn);
            }

            if (appNameTextView != null) {
                // 延迟200毫秒启动文字动画
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    Animation textFadeIn = new AlphaAnimation(0.0f, 1.0f);
                    textFadeIn.setDuration(1200);
                    appNameTextView.startAnimation(textFadeIn);
                }, 200);
            }

            if (welcomeTextView != null) {
                // 延迟400毫秒启动副标题动画
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    Animation subtitleFadeIn = new AlphaAnimation(0.0f, 1.0f);
                    subtitleFadeIn.setDuration(1000);
                    welcomeTextView.startAnimation(subtitleFadeIn);
                }, 400);
            }

            // 观察导航事件
            viewModel.getNavigationAction().observe(this, navigationAction -> {
                switch (navigationAction) {
                    case NAVIGATE_TO_LOGIN:
                        navigateToLoginScreen();
                        break;
                    case NAVIGATE_TO_MAIN:
                        navigateToMainScreen();
                        break;
                }
            });

            // 开始欢迎页倒计时
            // 注意：startSplashCountdown()方法可能不存在，需要检查SplashViewModel
            // 暂时使用Handler延迟跳转，避免编译错误
            new Handler().postDelayed(() -> {
                // 检查登录状态并导航
                navigateToLoginScreen();
            }, 2000); // 2秒后跳转
            // viewModel.startSplashCountdown();

        } catch (Exception e) {
            Log.e(TAG, "启动时发生错误: " + e.getMessage(), e);
            // 出现异常也尝试启动登录活动
            navigateToLoginScreen();
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            // 当窗口获得焦点时，再次设置全屏模式
            PerformanceUtils.setFullscreen(this, true);
        }
    }

    /**
     * 导航到登录界面
     */
    private void navigateToLoginScreen() {
        try {
            Intent intent = new Intent(SplashActivity.this, LoginActivity.class);
            startActivity(intent);
            finish(); // 结束当前活动
        } catch (Exception e) {
            Log.e(TAG, "跳转到登录界面时出错: " + e.getMessage(), e);
            Toast.makeText(this, "应用启动失败，请重试", Toast.LENGTH_LONG).show();
            finish();
        }
    }

    /**
     * 导航到主界面
     */
    private void navigateToMainScreen() {
        try {
            Intent intent = new Intent(SplashActivity.this, MainActivity.class);
            startActivity(intent);
            finish(); // 结束当前活动
        } catch (Exception e) {
            Log.e(TAG, "跳转到主界面时出错: " + e.getMessage(), e);
            Toast.makeText(this, "应用启动失败，请重试", Toast.LENGTH_LONG).show();
            finish();
        }
    }
}
