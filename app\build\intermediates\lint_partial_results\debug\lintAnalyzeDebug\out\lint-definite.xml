<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="incidents">

    <incident
        id="MissingSuperCall"
        severity="error"
        message="Overriding method should call `super.onStartCommand`">
        <fix-data/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/service/UnifiedPlaybackService.kt"
            line="225"
            column="18"
            startOffset="7517"
            endLine="225"
            endColumn="32"
            endOffset="7531"/>
    </incident>

    <incident
        id="RecyclerView"
        severity="error"
        message="Do not treat position as fixed; only use immediately and call `holder.getAdapterPosition()` to look it up later">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/CommentAdapter.kt"
            line="62"
            column="62"
            startOffset="2039"
            endLine="62"
            endColumn="75"
            endOffset="2052"/>
    </incident>

    <incident
        id="RecyclerView"
        severity="error"
        message="Do not treat position as fixed; only use immediately and call `holder.getAdapterPosition()` to look it up later">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/adapter/PlaylistAdapter.java"
            line="51"
            column="70"
            startOffset="1707"
            endLine="51"
            endColumn="82"
            endOffset="1719"/>
    </incident>

    <incident
        id="RecyclerView"
        severity="error"
        message="Do not treat position as fixed; only use immediately and call `holder.getAdapterPosition()` to look it up later">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/ReplyAdapter.kt"
            line="48"
            column="60"
            startOffset="1565"
            endLine="48"
            endColumn="73"
            endOffset="1578"/>
    </incident>

    <incident
        id="RecyclerView"
        severity="error"
        message="Do not treat position as fixed; only use immediately and call `holder.getAdapterPosition()` to look it up later">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/SongAdapter.kt"
            line="37"
            column="59"
            startOffset="1197"
            endLine="37"
            endColumn="72"
            endOffset="1210"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `placeholder` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="438"
            column="13"
            startOffset="12885"
            endLine="438"
            endColumn="45"
            endOffset="12917"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `error` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="443"
            column="13"
            startOffset="12984"
            endLine="443"
            endColumn="33"
            endOffset="13004"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `transform` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="448"
            column="13"
            startOffset="13068"
            endLine="448"
            endColumn="44"
            endOffset="13099"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `transform` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="450"
            column="13"
            startOffset="13151"
            endLine="450"
            endColumn="60"
            endOffset="13198"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `signature` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="455"
            column="13"
            startOffset="13273"
            endLine="455"
            endColumn="52"
            endOffset="13312"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `apply` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="506"
            column="9"
            startOffset="14847"
            endLine="506"
            endColumn="31"
            endOffset="14869"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `transition` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="511"
            column="17"
            startOffset="14963"
            endLine="511"
            endColumn="104"
            endOffset="15050"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `transition` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="513"
            column="17"
            startOffset="15088"
            endLine="513"
            endColumn="108"
            endOffset="15179"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `listener` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="521"
            column="17"
            startOffset="15333"
            endLine="542"
            endColumn="19"
            endOffset="16308"/>
    </incident>

    <incident
        id="CheckResult"
        severity="warning"
        message="The result of `listener` is not used">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="544"
            column="17"
            startOffset="16346"
            endLine="565"
            endColumn="19"
            endOffset="17335"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/CacheStats.kt"
            line="57"
            column="32"
            startOffset="1140"
            endLine="57"
            endColumn="73"
            endOffset="1181"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/EnhancedLyricParser.kt"
            line="268"
            column="16"
            startOffset="8552"
            endLine="268"
            endColumn="81"
            endOffset="8617"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/LyricParser.java"
            line="58"
            column="33"
            startOffset="1603"
            endLine="58"
            endColumn="44"
            endOffset="1614"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/LyricParser.java"
            line="254"
            column="16"
            startOffset="7655"
            endLine="254"
            endColumn="81"
            endOffset="7720"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/SongAdapter.kt"
            line="94"
            column="20"
            startOffset="3130"
            endLine="94"
            endColumn="64"
            endOffset="3174"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/TimeUtils.kt"
            line="16"
            column="16"
            startOffset="349"
            endLine="16"
            endColumn="60"
            endOffset="393"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/TimeUtils.kt"
            line="30"
            column="13"
            startOffset="771"
            endLine="30"
            endColumn="69"
            endOffset="827"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/TimeUtils.kt"
            line="32"
            column="13"
            startOffset="857"
            endLine="32"
            endColumn="57"
            endOffset="901"/>
    </incident>

    <incident
        id="NotSibling"
        severity="fatal"
        message="`@id/text_playlist_title` is not a sibling in the same `RelativeLayout`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_playlist.xml"
            line="27"
            column="13"
            startOffset="994"
            endLine="27"
            endColumn="61"
            endOffset="1042"/>
    </incident>

    <incident
        id="NotSibling"
        severity="fatal"
        message="`@+id/layout_like` is not a sibling in the same `ConstraintLayout`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml"
            line="193"
            column="9"
            startOffset="7873"
            endLine="193"
            endColumn="63"
            endOffset="7927"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="16"
            column="9"
            startOffset="449"
            endLine="16"
            endColumn="21"
            endOffset="461"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/player/PlayerFragment.kt"
            line="973"
            column="75"
            startOffset="31549"
            endLine="973"
            endColumn="79"
            endOffset="31553"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_playing_disc.xml"
            line="3"
            column="20"
            startOffset="125"
            endLine="3"
            endColumn="25"
            endOffset="130"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0">
        <fix-replace
            description="Change to 1.7.0"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="73"
            column="20"
            startOffset="1814"
            endLine="73"
            endColumn="56"
            endOffset="1850"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.11.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="74"
            column="20"
            startOffset="1870"
            endLine="74"
            endColumn="65"
            endOffset="1915"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="75"
            column="20"
            startOffset="1935"
            endLine="75"
            endColumn="70"
            endOffset="1985"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="78"
            column="20"
            startOffset="2123"
            endLine="78"
            endColumn="54"
            endOffset="2157"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-exoplayer than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="90"
            column="20"
            startOffset="2643"
            endLine="90"
            endColumn="60"
            endOffset="2683"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-ui than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="91"
            column="20"
            startOffset="2703"
            endLine="91"
            endColumn="53"
            endOffset="2736"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-session than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="92"
            column="20"
            startOffset="2756"
            endLine="92"
            endColumn="58"
            endOffset="2794"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-common than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="93"
            column="20"
            startOffset="2814"
            endLine="93"
            endColumn="57"
            endOffset="2851"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-datasource-okhttp than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="94"
            column="20"
            startOffset="2871"
            endLine="94"
            endColumn="68"
            endOffset="2919"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.zxing:core than 3.4.1 is available: 3.5.1">
        <fix-replace
            description="Change to 3.5.1"
            family="Update versions"
            oldString="3.4.1"
            replacement="3.5.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="113"
            column="20"
            startOffset="3537"
            endLine="113"
            endColumn="49"
            endOffset="3566"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.journeyapps:zxing-android-embedded than 4.2.0 is available: 4.3.0">
        <fix-replace
            description="Change to 4.3.0"
            family="Update versions"
            oldString="4.2.0"
            replacement="4.3.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="114"
            column="20"
            startOffset="3586"
            endLine="114"
            endColumn="66"
            endOffset="3632"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="122"
            column="20"
            startOffset="3826"
            endLine="122"
            endColumn="70"
            endOffset="3876"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="123"
            column="20"
            startOffset="3896"
            endLine="123"
            endColumn="69"
            endOffset="3945"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="124"
            column="20"
            startOffset="3965"
            endLine="124"
            endColumn="68"
            endOffset="4013"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-common-java8 than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="125"
            column="20"
            startOffset="4033"
            endLine="125"
            endColumn="69"
            endOffset="4082"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.airbnb.android:lottie than 6.1.0 is available: 6.3.0">
        <fix-replace
            description="Change to 6.3.0"
            family="Update versions"
            oldString="6.1.0"
            replacement="6.3.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="132"
            column="20"
            startOffset="4286"
            endLine="132"
            endColumn="53"
            endOffset="4319"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-fragment-ktx than 2.7.5 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.5"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="138"
            column="20"
            startOffset="4476"
            endLine="138"
            endColumn="71"
            endOffset="4527"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-ui-ktx than 2.7.5 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.5"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="139"
            column="20"
            startOffset="4547"
            endLine="139"
            endColumn="65"
            endOffset="4592"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="142"
            column="20"
            startOffset="4628"
            endLine="142"
            endColumn="54"
            endOffset="4662"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="143"
            column="20"
            startOffset="4682"
            endLine="143"
            endColumn="50"
            endOffset="4712"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="145"
            column="9"
            startOffset="4744"
            endLine="145"
            endColumn="44"
            endOffset="4779"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="148"
            column="31"
            startOffset="4855"
            endLine="148"
            endColumn="62"
            endOffset="4886"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="149"
            column="31"
            startOffset="4917"
            endLine="149"
            endColumn="75"
            endOffset="4961"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-replace
            description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to containing class &apos;Factory&apos;"
            oldString="_lint_insert_begin_"
            replacement="@androidx.media3.common.util.UnstableApi "
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/data/source/MusicDataSource.kt"
                startOffset="0"
                endOffset="0"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/data/source/MusicDataSource.kt"
            line="700"
            column="22"
            startOffset="20801"
            endLine="700"
            endColumn="38"
            endOffset="20817"/>
    </incident>

    <incident
        id="UnsafeOptInUsageError"
        severity="error"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`">
        <fix-alternatives>
            <fix-replace
                description="Add &apos;@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)&apos; annotation to &apos;createDataSource&apos;"
                oldString="_lint_insert_begin_"
                replacement="@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class) "
                shortenNames="true"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/data/source/MusicDataSource.kt"
                    startOffset="20788"
                    endOffset="20796"/>
            </fix-replace>
            <fix-replace
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to &apos;createDataSource&apos;"
                oldString="_lint_insert_begin_"
                replacement="@androidx.media3.common.util.UnstableApi "
                shortenNames="true"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/data/source/MusicDataSource.kt"
                    startOffset="20788"
                    endOffset="20796"/>
            </fix-replace>
            <fix-replace
                description="Add &apos;@androidx.media3.common.util.UnstableApi&apos; annotation to containing class &apos;Factory&apos;"
                oldString="_lint_insert_begin_"
                replacement="@androidx.media3.common.util.UnstableApi "
                shortenNames="true"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/data/source/MusicDataSource.kt"
                    startOffset="0"
                    endOffset="0"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/data/source/MusicDataSource.kt"
            line="701"
            column="45"
            startOffset="20878"
            endLine="701"
            endColumn="61"
            endOffset="20894"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="63"
            column="13"
            startOffset="2678"
            endLine="63"
            endColumn="50"
            endOffset="2715"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="75"
            column="13"
            startOffset="3131"
            endLine="75"
            endColumn="50"
            endOffset="3168"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="82"
            column="13"
            startOffset="3384"
            endLine="82"
            endColumn="50"
            endOffset="3421"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ImageUtils.kt"
            line="469"
            column="56"
            startOffset="13796"
            endLine="469"
            endColumn="69"
            endOffset="13809"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;@color/sakura_text_secondary&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;@color/sakura_text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/sakura_text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="64"
            column="17"
            startOffset="2365"
            endLine="64"
            endColumn="60"
            endOffset="2408"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;#FFFFFF&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;#FFFFFF&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#FFFFFF"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="154"
            column="21"
            startOffset="5780"
            endLine="154"
            endColumn="43"
            endOffset="5802"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;#3498db&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;#3498db&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#3498db"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="165"
            column="21"
            startOffset="6276"
            endLine="165"
            endColumn="43"
            endOffset="6298"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;#FFFFFF&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;#FFFFFF&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#FFFFFF"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="176"
            column="21"
            startOffset="6764"
            endLine="176"
            endColumn="43"
            endOffset="6786"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;#3498db&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;#3498db&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#3498db"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="203"
            column="25"
            startOffset="7918"
            endLine="203"
            endColumn="47"
            endOffset="7940"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;#E74C3C&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;#E74C3C&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#E74C3C"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="231"
            column="25"
            startOffset="9183"
            endLine="231"
            endColumn="47"
            endOffset="9205"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Set drawableStartCompat=&quot;@drawable/ic_favorite&quot; and Delete drawableStart">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_favorite&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_favorite"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="95"
            column="29"
            startOffset="4431"
            endLine="95"
            endColumn="74"
            endOffset="4476"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableTint` instead of `android:drawableTint`">
        <fix-composite
            description="Set drawableTint=&quot;#F1C40F&quot; and Delete drawableTint">
            <fix-attribute
                description="Set drawableTint=&quot;#F1C40F&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableTint"
                value="#F1C40F"/>
            <fix-attribute
                description="Delete drawableTint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableTint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="97"
            column="29"
            startOffset="4563"
            endLine="97"
            endColumn="59"
            endOffset="4593"/>
    </incident>

    <incident
        id="AcceptsUserCertificates"
        severity="warning"
        message="The Network Security Configuration allows the use of user certificates in the release version of your app">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="6"
            column="13"
            startOffset="199"
            endLine="6"
            endColumn="40"
            endOffset="226"/>
    </incident>

    <incident
        id="InsecureBaseConfiguration"
        severity="warning"
        message="Insecure Base Configuration">
        <fix-replace
            description="Replace with false"
            replacement="false"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
                startOffset="111"
                endOffset="115"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="3"
            column="45"
            startOffset="111"
            endLine="3"
            endColumn="49"
            endOffset="115"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/adapter/TopListAdapter.java"
            line="66"
            column="9"
            startOffset="2125"
            endLine="66"
            endColumn="31"
            endOffset="2147"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/discovery/TopListFragment.java"
            line="108"
            column="25"
            startOffset="3616"
            endLine="108"
            endColumn="55"
            endOffset="3646"/>
    </incident>

    <incident
        id="Recycle"
        severity="warning"
        message="This animation should be started with `#start()`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/AlbumRotationController.kt"
            line="445"
            column="27"
            startOffset="14470"
            endLine="445"
            endColumn="38"
            endOffset="14481"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/AlbumRotationController.kt"
            line="104"
            column="17"
            startOffset="3393"
            endLine="104"
            endColumn="63"
            endOffset="3439"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/AlbumRotationController.kt"
            line="117"
            column="17"
            startOffset="3910"
            endLine="117"
            endColumn="63"
            endOffset="3956"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/error/GlobalErrorHandler.kt"
            line="164"
            column="13"
            startOffset="5820"
            endLine="164"
            endColumn="59"
            endOffset="5866"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/main/MainActivity.java"
            line="423"
            column="13"
            startOffset="14473"
            endLine="423"
            endColumn="59"
            endOffset="14519"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/NetworkUtils.kt"
            line="28"
            column="13"
            startOffset="629"
            endLine="28"
            endColumn="59"
            endOffset="675"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/NetworkUtils.kt"
            line="53"
            column="13"
            startOffset="1564"
            endLine="53"
            endColumn="59"
            endOffset="1610"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/NetworkUtils.kt"
            line="78"
            column="13"
            startOffset="2513"
            endLine="78"
            endColumn="59"
            endOffset="2559"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="This folder configuration (`v24`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `drawable`.">
        <fix-data file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v24" folderName="drawable" requiresApi="24"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v24"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `AlbumArtCache` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/AlbumArtCache.kt"
            line="33"
            column="9"
            startOffset="972"
            endLine="35"
            endColumn="52"
            endOffset="1049"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `CacheManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/CacheManager.kt"
            line="376"
            column="9"
            startOffset="11154"
            endLine="378"
            endColumn="51"
            endOffset="11230"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `EnhancedImageCache` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/EnhancedImageCache.kt"
            line="34"
            column="9"
            startOffset="1148"
            endLine="36"
            endColumn="57"
            endOffset="1230"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml"
            line="97"
            column="14"
            startOffset="4037"
            endLine="97"
            endColumn="26"
            endOffset="4049"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml"
            line="135"
            column="14"
            startOffset="5599"
            endLine="135"
            endColumn="26"
            endOffset="5611"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (906 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="9"
            column="27"
            startOffset="297"
            endLine="9"
            endColumn="933"
            endOffset="1203"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/icon_settings.xml"
            line="9"
            column="27"
            startOffset="301"
            endLine="9"
            endColumn="931"
            endOffset="1205"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="36"
            column="6"
            startOffset="1197"
            endLine="36"
            endColumn="18"
            endOffset="1209"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="26"
            column="6"
            startOffset="878"
            endLine="26"
            endColumn="18"
            endOffset="890"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/splash_background` with a theme that also paints a background (inferred theme is `@style/FullScreenTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="7"
            column="5"
            startOffset="306"
            endLine="7"
            endColumn="50"
            endOffset="351"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?colorBackground` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="8"
            column="5"
            startOffset="339"
            endLine="8"
            endColumn="42"
            endOffset="376"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?colorBackground` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_intelligence.xml"
            line="8"
            column="5"
            startOffset="339"
            endLine="8"
            endColumn="42"
            endOffset="376"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_dark` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_comment.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="48"
            endOffset="384"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#000022` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="6"
            column="5"
            startOffset="250"
            endLine="6"
            endColumn="33"
            endOffset="278"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_color` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_intelligence.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="49"
            endOffset="385"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="7"
            column="5"
            startOffset="343"
            endLine="7"
            endColumn="38"
            endOffset="376"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_hot_search.xml"
            line="10"
            column="5"
            startOffset="426"
            endLine="10"
            endColumn="64"
            endOffset="485"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_reply.xml"
            line="10"
            column="5"
            startOffset="436"
            endLine="10"
            endColumn="56"
            endOffset="487"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_search_suggest.xml"
            line="10"
            column="5"
            startOffset="427"
            endLine="10"
            endColumn="64"
            endOffset="486"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_song.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="56"
            endOffset="392"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#EFEFEF` with a theme that also paints a background (inferred theme is `@style/Theme.AIMusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_controls.xml"
            line="5"
            column="5"
            startOffset="197"
            endLine="5"
            endColumn="33"
            endOffset="225"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="28"
            startOffset="1372"
            endLine="31"
            endColumn="34"
            endOffset="1378"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="70"
            column="33"
            startOffset="2864"
            endLine="70"
            endColumn="41"
            endOffset="2872"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/cherry_blossom_car.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/cherry_blossom_car.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logo.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.jpg"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logo_music.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo_music.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/next.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/next.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/pause.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/pause.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/play.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/play.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/previous.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/previous.png"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
                    startOffset="6225"
                    endOffset="7208"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
                    startOffset="6425"
                    endOffset="6809"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
                    startOffset="6821"
                    endOffset="7187"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="175"
            column="10"
            startOffset="6426"
            endLine="175"
            endColumn="16"
            endOffset="6432"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
                    startOffset="6225"
                    endOffset="7208"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
                    startOffset="6425"
                    endOffset="6809"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
                    startOffset="6821"
                    endOffset="7187"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="185"
            column="10"
            startOffset="6822"
            endLine="185"
            endColumn="16"
            endOffset="6828"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/page_player_comments.xml"
            line="52"
            column="10"
            startOffset="1755"
            endLine="52"
            endColumn="18"
            endOffset="1763"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_online_song.xml"
            line="46"
            column="17"
            startOffset="1740"
            endLine="46"
            endColumn="40"
            endOffset="1763"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_song.xml"
            line="63"
            column="9"
            startOffset="2522"
            endLine="63"
            endColumn="32"
            endOffset="2545"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="52"
            column="6"
            startOffset="1826"
            endLine="52"
            endColumn="14"
            endOffset="1834"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="81"
            column="10"
            startOffset="2882"
            endLine="81"
            endColumn="18"
            endOffset="2890"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="118"
            column="14"
            startOffset="4265"
            endLine="118"
            endColumn="22"
            endOffset="4273"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/page_player_comments.xml"
            line="52"
            column="10"
            startOffset="1755"
            endLine="52"
            endColumn="18"
            endOffset="1763"/>
    </incident>

    <incident
        id="KtxExtensionAvailable"
        severity="informational"
        message="Add suffix `-ktx` to enable the Kotlin extensions for this library">
        <fix-replace
            description="Replace with KTX dependency"
            oldString="androidx.palette:palette"
            replacement="androidx.palette:palette-ktx"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="107"
            column="20"
            startOffset="3363"
            endLine="107"
            endColumn="52"
            endOffset="3395"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt"
            line="41"
            column="33"
            startOffset="1140"
            endLine="85"
            endColumn="10"
            endOffset="2844"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt"
            line="107"
            column="33"
            startOffset="3380"
            endLine="146"
            endColumn="10"
            endOffset="4867"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/login/LoginActivity.kt"
            line="120"
            column="39"
            startOffset="3501"
            endLine="130"
            endColumn="14"
            endOffset="3945"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view `LyricView` overrides `onTouchEvent` but not `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/player/LyricView.kt"
            line="255"
            column="18"
            startOffset="7105"
            endLine="255"
            endColumn="30"
            endOffset="7117"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``ViewPager2`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/player/PlayerFragment.kt"
            line="609"
            column="9"
            startOffset="19934"
            endLine="674"
            endColumn="10"
            endOffset="22736"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="37"
            column="14"
            startOffset="1201"
            endLine="37"
            endColumn="23"
            endOffset="1210"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="10"
            column="6"
            startOffset="370"
            endLine="10"
            endColumn="15"
            endOffset="379"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="37"
            column="10"
            startOffset="1324"
            endLine="37"
            endColumn="19"
            endOffset="1333"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="59"
            column="14"
            startOffset="2133"
            endLine="59"
            endColumn="23"
            endOffset="2142"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="58"
            column="18"
            startOffset="1942"
            endLine="58"
            endColumn="27"
            endOffset="1951"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="146"
            column="18"
            startOffset="5380"
            endLine="146"
            endColumn="29"
            endOffset="5391"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="157"
            column="18"
            startOffset="5876"
            endLine="157"
            endColumn="29"
            endOffset="5887"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="168"
            column="18"
            startOffset="6372"
            endLine="168"
            endColumn="29"
            endOffset="6383"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="196"
            column="22"
            startOffset="7523"
            endLine="196"
            endColumn="33"
            endOffset="7534"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="224"
            column="22"
            startOffset="8779"
            endLine="224"
            endColumn="33"
            endOffset="8790"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_intelligence.xml"
            line="36"
            column="14"
            startOffset="1426"
            endLine="36"
            endColumn="23"
            endOffset="1435"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="29"
            column="18"
            startOffset="1240"
            endLine="29"
            endColumn="27"
            endOffset="1249"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml"
            line="105"
            column="18"
            startOffset="4369"
            endLine="105"
            endColumn="27"
            endOffset="4378"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml"
            line="143"
            column="18"
            startOffset="5931"
            endLine="143"
            endColumn="27"
            endOffset="5940"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml"
            line="176"
            column="14"
            startOffset="7199"
            endLine="176"
            endColumn="23"
            endOffset="7208"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_playlist.xml"
            line="16"
            column="10"
            startOffset="553"
            endLine="16"
            endColumn="19"
            endOffset="562"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_reply.xml"
            line="87"
            column="14"
            startOffset="3398"
            endLine="87"
            endColumn="23"
            endOffset="3407"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/view_album_cover.xml"
            line="8"
            column="6"
            startOffset="248"
            endLine="8"
            endColumn="15"
            endOffset="257"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/view_album_cover.xml"
            line="16"
            column="6"
            startOffset="479"
            endLine="16"
            endColumn="15"
            endOffset="488"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/view_album_cover.xml"
            line="25"
            column="6"
            startOffset="758"
            endLine="25"
            endColumn="15"
            endOffset="767"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/CommentAdapter.kt"
            line="116"
            column="42"
            startOffset="3659"
            endLine="116"
            endColumn="70"
            endOffset="3687"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/CommentAdapter.kt"
            line="137"
            column="47"
            startOffset="4600"
            endLine="137"
            endColumn="72"
            endOffset="4625"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/login/LoginActivity.kt"
            line="299"
            column="39"
            startOffset="10072"
            endLine="299"
            endColumn="69"
            endOffset="10102"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/player/PlayerFragment.kt"
            line="1001"
            column="38"
            startOffset="32494"
            endLine="1001"
            endColumn="61"
            endOffset="32517"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/ReplyAdapter.kt"
            line="127"
            column="47"
            startOffset="4071"
            endLine="127"
            endColumn="73"
            endOffset="4097"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/adapter/TopListAdapter.java"
            line="41"
            column="42"
            startOffset="1440"
            endLine="41"
            endColumn="71"
            endOffset="1469"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="228"
            column="35"
            startOffset="8300"
            endLine="228"
            endColumn="53"
            endOffset="8318"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="228"
            column="36"
            startOffset="8301"
            endLine="228"
            endColumn="39"
            endOffset="8304"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="244"
            column="47"
            startOffset="8767"
            endLine="244"
            endColumn="50"
            endOffset="8770"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="248"
            column="46"
            startOffset="8917"
            endLine="248"
            endColumn="48"
            endOffset="8919"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="252"
            column="51"
            startOffset="9072"
            endLine="252"
            endColumn="74"
            endOffset="9095"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="256"
            column="46"
            startOffset="9232"
            endLine="256"
            endColumn="49"
            endOffset="9235"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="268"
            column="37"
            startOffset="9571"
            endLine="268"
            endColumn="48"
            endOffset="9582"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="269"
            column="37"
            startOffset="9635"
            endLine="269"
            endColumn="57"
            endOffset="9655"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/profile/UserProfileFragment.kt"
            line="273"
            column="45"
            startOffset="9768"
            endLine="273"
            endColumn="64"
            endOffset="9787"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;背景图片&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="16"
            column="9"
            startOffset="599"
            endLine="16"
            endColumn="42"
            endOffset="632"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;欢迎使用轻聆&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="54"
            column="17"
            startOffset="1926"
            endLine="54"
            endColumn="38"
            endOffset="1947"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;享受音乐，享受生活&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="71"
            column="17"
            startOffset="2620"
            endLine="71"
            endColumn="41"
            endOffset="2644"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;扫码登录图标&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="107"
            column="25"
            startOffset="4218"
            endLine="107"
            endColumn="60"
            endOffset="4253"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;扫码登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="114"
            column="25"
            startOffset="4532"
            endLine="114"
            endColumn="44"
            endOffset="4551"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;手机登录图标&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="146"
            column="25"
            startOffset="6007"
            endLine="146"
            endColumn="60"
            endOffset="6042"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;手机号登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="153"
            column="25"
            startOffset="6321"
            endLine="153"
            endColumn="45"
            endOffset="6341"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;游客登录图标&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="184"
            column="25"
            startOffset="7741"
            endLine="184"
            endColumn="60"
            endOffset="7776"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;游客登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="191"
            column="25"
            startOffset="8055"
            endLine="191"
            endColumn="44"
            endOffset="8074"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;轻聆音乐 v1.0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="210"
            column="17"
            startOffset="8873"
            endLine="210"
            endColumn="41"
            endOffset="8897"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;播放器&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="72"
            column="17"
            startOffset="2679"
            endLine="72"
            endColumn="49"
            endOffset="2711"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;我的音乐库&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="97"
            column="17"
            startOffset="3611"
            endLine="97"
            endColumn="51"
            endOffset="3645"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;音乐探索&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="122"
            column="17"
            startOffset="4544"
            endLine="122"
            endColumn="50"
            endOffset="4577"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;驾驶模式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="147"
            column="17"
            startOffset="5474"
            endLine="147"
            endColumn="50"
            endOffset="5507"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;用户中心&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="172"
            column="17"
            startOffset="6403"
            endLine="172"
            endColumn="50"
            endOffset="6436"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;设置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="197"
            column="17"
            startOffset="7335"
            endLine="197"
            endColumn="48"
            endOffset="7366"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;菜单&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="220"
            column="9"
            startOffset="8170"
            endLine="220"
            endColumn="40"
            endOffset="8201"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;歌曲名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="57"
            column="21"
            startOffset="2125"
            endLine="57"
            endColumn="40"
            endOffset="2144"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;歌手名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="68"
            column="21"
            startOffset="2596"
            endLine="68"
            endColumn="40"
            endOffset="2615"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="125"
            column="21"
            startOffset="4709"
            endLine="125"
            endColumn="41"
            endOffset="4729"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="143"
            column="21"
            startOffset="5539"
            endLine="143"
            endColumn="41"
            endOffset="5559"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;轻聆&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="36"
            column="13"
            startOffset="1284"
            endLine="36"
            endColumn="30"
            endOffset="1301"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;您的专属智能车载音乐伴侣&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="50"
            column="13"
            startOffset="1773"
            endLine="50"
            endColumn="40"
            endOffset="1800"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Version 1.0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="68"
            column="9"
            startOffset="2359"
            endLine="68"
            endColumn="35"
            endOffset="2385"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;评论&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="25"
            column="13"
            startOffset="925"
            endLine="25"
            endColumn="30"
            endOffset="942"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;关闭&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="45"
            column="13"
            startOffset="1726"
            endLine="45"
            endColumn="44"
            endOffset="1757"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;暂无评论&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="69"
            column="9"
            startOffset="2539"
            endLine="69"
            endColumn="28"
            endOffset="2558"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;发表评论...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="122"
            column="13"
            startOffset="4458"
            endLine="122"
            endColumn="35"
            endOffset="4480"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;发送&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="134"
            column="13"
            startOffset="4898"
            endLine="134"
            endColumn="30"
            endOffset="4915"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;评论发送成功&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="149"
            column="9"
            startOffset="5450"
            endLine="149"
            endColumn="30"
            endOffset="5471"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;心动模式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_heart_mode.xml"
            line="22"
            column="13"
            startOffset="787"
            endLine="22"
            endColumn="32"
            endOffset="806"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;刷新&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_heart_mode.xml"
            line="39"
            column="13"
            startOffset="1407"
            endLine="39"
            endColumn="44"
            endOffset="1438"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;关闭&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_heart_mode.xml"
            line="48"
            column="13"
            startOffset="1774"
            endLine="48"
            endColumn="44"
            endOffset="1805"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;根据当前歌曲为您推荐相似的音乐，开启心动模式后将自动播放这些歌曲。&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_heart_mode.xml"
            line="63"
            column="9"
            startOffset="2192"
            endLine="63"
            endColumn="57"
            endOffset="2240"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;正在寻找相似歌曲...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_heart_mode.xml"
            line="99"
            column="17"
            startOffset="3516"
            endLine="99"
            endColumn="43"
            endOffset="3542"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;开启心动模式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_heart_mode.xml"
            line="112"
            column="9"
            startOffset="3949"
            endLine="112"
            endColumn="30"
            endOffset="3970"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;心动模式推荐&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_intelligence.xml"
            line="25"
            column="13"
            startOffset="930"
            endLine="25"
            endColumn="34"
            endOffset="951"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;关闭&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_intelligence.xml"
            line="34"
            column="13"
            startOffset="1295"
            endLine="34"
            endColumn="44"
            endOffset="1326"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;暂无推荐歌曲&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_intelligence.xml"
            line="58"
            column="9"
            startOffset="2123"
            endLine="58"
            endColumn="30"
            endOffset="2144"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;手机号登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="13"
            column="9"
            startOffset="475"
            endLine="13"
            endColumn="29"
            endOffset="495"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入手机号和密码登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="29"
            column="13"
            startOffset="1022"
            endLine="29"
            endColumn="39"
            endOffset="1048"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;验证码登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="37"
            column="13"
            startOffset="1337"
            endLine="37"
            endColumn="33"
            endOffset="1357"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;手机号码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="48"
            column="9"
            startOffset="1707"
            endLine="48"
            endColumn="28"
            endOffset="1726"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入手机号码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="58"
            column="9"
            startOffset="2074"
            endLine="58"
            endColumn="31"
            endOffset="2096"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;密码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="77"
            column="13"
            startOffset="2753"
            endLine="77"
            endColumn="30"
            endOffset="2770"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入密码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="87"
            column="13"
            startOffset="3157"
            endLine="87"
            endColumn="33"
            endOffset="3177"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;验证码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="108"
            column="13"
            startOffset="3916"
            endLine="108"
            endColumn="31"
            endOffset="3934"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入验证码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="124"
            column="17"
            startOffset="4549"
            endLine="124"
            endColumn="38"
            endOffset="4570"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;获取验证码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="137"
            column="17"
            startOffset="5135"
            endLine="137"
            endColumn="37"
            endOffset="5155"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;取消&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="182"
            column="13"
            startOffset="6729"
            endLine="182"
            endColumn="30"
            endOffset="6746"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_phone_login.xml"
            line="192"
            column="13"
            startOffset="7116"
            endLine="192"
            endColumn="30"
            endOffset="7133"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;播放列表&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_playlist.xml"
            line="19"
            column="13"
            startOffset="712"
            endLine="19"
            endColumn="32"
            endOffset="731"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;关闭&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_playlist.xml"
            line="41"
            column="13"
            startOffset="1615"
            endLine="41"
            endColumn="44"
            endOffset="1646"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;播放列表为空&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_playlist.xml"
            line="57"
            column="9"
            startOffset="2202"
            endLine="57"
            endColumn="30"
            endOffset="2223"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;清空播放列表&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_playlist.xml"
            line="66"
            column="9"
            startOffset="2489"
            endLine="66"
            endColumn="30"
            endOffset="2510"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;扫码登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="12"
            column="9"
            startOffset="441"
            endLine="12"
            endColumn="28"
            endOffset="460"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请使用网易云音乐APP扫描二维码登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="23"
            column="9"
            startOffset="828"
            endLine="23"
            endColumn="42"
            endOffset="861"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;二维码加载失败&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="72"
            column="17"
            startOffset="2699"
            endLine="72"
            endColumn="39"
            endOffset="2721"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;点击重新加载&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="83"
            column="17"
            startOffset="3185"
            endLine="83"
            endColumn="38"
            endOffset="3206"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;正在加载二维码...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="93"
            column="13"
            startOffset="3546"
            endLine="93"
            endColumn="38"
            endOffset="3571"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;打开网易云音乐APP，点击右上角+，选择扫一扫扫描上方二维码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="105"
            column="9"
            startOffset="3971"
            endLine="105"
            endColumn="54"
            endOffset="4016"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;取消&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_qr_login.xml"
            line="116"
            column="9"
            startOffset="4412"
            endLine="116"
            endColumn="26"
            endOffset="4429"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;12:34&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="21"
            column="13"
            startOffset="744"
            endLine="21"
            endColumn="33"
            endOffset="764"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;6月15日 周四&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="30"
            column="13"
            startOffset="1049"
            endLine="30"
            endColumn="36"
            endOffset="1072"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;歌曲名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="77"
            column="17"
            startOffset="2738"
            endLine="77"
            endColumn="36"
            endOffset="2757"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;歌手名&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="90"
            column="17"
            startOffset="3244"
            endLine="90"
            endColumn="35"
            endOffset="3262"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="123"
            column="21"
            startOffset="4478"
            endLine="123"
            endColumn="40"
            endOffset="4497"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="133"
            column="21"
            startOffset="4915"
            endLine="133"
            endColumn="40"
            endOffset="4934"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;语音控制&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="209"
            column="25"
            startOffset="8179"
            endLine="209"
            endColumn="44"
            endOffset="8198"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;退出驾驶模式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_driving_mode.xml"
            line="237"
            column="25"
            startOffset="9444"
            endLine="237"
            endColumn="46"
            endOffset="9465"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;根据当前歌曲为您推荐的相似歌曲&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_intelligence.xml"
            line="80"
            column="17"
            startOffset="3380"
            endLine="80"
            endColumn="47"
            endOffset="3410"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;暂无相似歌曲推荐&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_intelligence.xml"
            line="119"
            column="9"
            startOffset="4958"
            endLine="119"
            endColumn="32"
            endOffset="4981"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;功能开发中...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_placeholder.xml"
            line="11"
            column="9"
            startOffset="452"
            endLine="11"
            endColumn="32"
            endOffset="475"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;背景&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="14"
            column="9"
            startOffset="515"
            endLine="14"
            endColumn="40"
            endOffset="546"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;唱臂&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="68"
            column="21"
            startOffset="2579"
            endLine="68"
            endColumn="52"
            endOffset="2610"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;黑胶唱片&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="78"
            column="21"
            startOffset="3006"
            endLine="78"
            endColumn="54"
            endOffset="3039"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;专辑封面&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="89"
            column="21"
            startOffset="3496"
            endLine="89"
            endColumn="54"
            endOffset="3529"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="217"
            column="17"
            startOffset="8658"
            endLine="217"
            endColumn="37"
            endOffset="8678"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="239"
            column="17"
            startOffset="9587"
            endLine="239"
            endColumn="37"
            endOffset="9607"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;收藏&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="264"
            column="21"
            startOffset="10544"
            endLine="264"
            endColumn="52"
            endOffset="10575"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;上一首&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="277"
            column="21"
            startOffset="11130"
            endLine="277"
            endColumn="53"
            endOffset="11162"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;播放/暂停&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="297"
            column="25"
            startOffset="12057"
            endLine="297"
            endColumn="59"
            endOffset="12091"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;下一首&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="308"
            column="21"
            startOffset="12513"
            endLine="308"
            endColumn="53"
            endOffset="12545"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;循环模式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="321"
            column="21"
            startOffset="13104"
            endLine="321"
            endColumn="54"
            endOffset="13137"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;播放列表&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="334"
            column="21"
            startOffset="13697"
            endLine="334"
            endColumn="54"
            endOffset="13730"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;心动模式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="347"
            column="21"
            startOffset="14298"
            endLine="347"
            endColumn="54"
            endOffset="14331"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;评论&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="360"
            column="21"
            startOffset="14887"
            endLine="360"
            endColumn="52"
            endOffset="14918"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;分享&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_player.xml"
            line="373"
            column="21"
            startOffset="15470"
            endLine="373"
            endColumn="52"
            endOffset="15501"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;用户名&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="75"
            column="25"
            startOffset="3449"
            endLine="75"
            endColumn="43"
            endOffset="3467"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;VIP会员&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="99"
            column="29"
            startOffset="4672"
            endLine="99"
            endColumn="49"
            endOffset="4692"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Lv.6&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="114"
            column="29"
            startOffset="5461"
            endLine="114"
            endColumn="48"
            endOffset="5480"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;音乐是生活的调味剂，让心灵得到治愈&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="127"
            column="25"
            startOffset="6041"
            endLine="127"
            endColumn="57"
            endOffset="6073"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;268&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="163"
            column="33"
            startOffset="7748"
            endLine="163"
            endColumn="51"
            endOffset="7766"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;收藏歌曲&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="172"
            column="33"
            startOffset="8227"
            endLine="172"
            endColumn="52"
            endOffset="8246"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;32&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="195"
            column="33"
            startOffset="9297"
            endLine="195"
            endColumn="50"
            endOffset="9314"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;创建歌单&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="204"
            column="33"
            startOffset="9775"
            endLine="204"
            endColumn="52"
            endOffset="9794"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;86&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="227"
            column="33"
            startOffset="10852"
            endLine="227"
            endColumn="50"
            endOffset="10869"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;关注歌手&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="236"
            column="33"
            startOffset="11330"
            endLine="236"
            endColumn="52"
            endOffset="11349"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;124&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="259"
            column="33"
            startOffset="12400"
            endLine="259"
            endColumn="51"
            endOffset="12418"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;收听时长(小时)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="268"
            column="33"
            startOffset="12879"
            endLine="268"
            endColumn="56"
            endOffset="12902"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;账户信息&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="308"
            column="21"
            startOffset="14508"
            endLine="308"
            endColumn="40"
            endOffset="14527"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;更多 >&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="317"
            column="21"
            startOffset="14887"
            endLine="317"
            endColumn="40"
            endOffset="14906"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;手机号&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="345"
            column="29"
            startOffset="16022"
            endLine="345"
            endColumn="47"
            endOffset="16040"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;138****6789&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="353"
            column="29"
            startOffset="16399"
            endLine="353"
            endColumn="55"
            endOffset="16425"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;邮箱&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="373"
            column="29"
            startOffset="17260"
            endLine="373"
            endColumn="46"
            endOffset="17277"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="381"
            column="29"
            startOffset="17636"
            endLine="381"
            endColumn="64"
            endOffset="17671"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;会员状态&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="401"
            column="29"
            startOffset="18508"
            endLine="401"
            endColumn="48"
            endOffset="18527"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;年费会员 (有效期至2024年12月)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="409"
            column="29"
            startOffset="18891"
            endLine="409"
            endColumn="63"
            endOffset="18925"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;注册时间&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="429"
            column="29"
            startOffset="19749"
            endLine="429"
            endColumn="48"
            endOffset="19768"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2021年03月15日&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="437"
            column="29"
            startOffset="20135"
            endLine="437"
            endColumn="55"
            endOffset="20161"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;退出登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="451"
            column="17"
            startOffset="20729"
            endLine="451"
            endColumn="36"
            endOffset="20748"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;加载失败，点击重试&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_user_profile.xml"
            line="470"
            column="9"
            startOffset="21325"
            endLine="470"
            endColumn="33"
            endOffset="21349"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;回复&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml"
            line="153"
            column="21"
            startOffset="6364"
            endLine="153"
            endColumn="38"
            endOffset="6381"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;VIP&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_online_song.xml"
            line="44"
            column="17"
            startOffset="1661"
            endLine="44"
            endColumn="35"
            endOffset="1679"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;排行榜名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_playlist.xml"
            line="35"
            column="17"
            startOffset="1266"
            endLine="35"
            endColumn="37"
            endOffset="1286"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;排行榜描述&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_playlist.xml"
            line="47"
            column="17"
            startOffset="1752"
            endLine="47"
            endColumn="37"
            endOffset="1772"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;每日更新&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_playlist.xml"
            line="55"
            column="17"
            startOffset="2059"
            endLine="55"
            endColumn="36"
            endOffset="2078"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;歌曲封面&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_playlist_song.xml"
            line="18"
            column="9"
            startOffset="652"
            endLine="18"
            endColumn="42"
            endOffset="685"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;回复&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_reply.xml"
            line="110"
            column="13"
            startOffset="4276"
            endLine="110"
            endColumn="30"
            endOffset="4293"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;类型图标&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_search_suggest.xml"
            line="19"
            column="9"
            startOffset="732"
            endLine="19"
            endColumn="42"
            endOffset="765"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;排行榜封面&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_top_list.xml"
            line="20"
            column="13"
            startOffset="707"
            endLine="20"
            endColumn="47"
            endOffset="741"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;评论&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/page_player_comments.xml"
            line="17"
            column="13"
            startOffset="567"
            endLine="17"
            endColumn="30"
            endOffset="584"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;(0)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/page_player_comments.xml"
            line="30"
            column="13"
            startOffset="1083"
            endLine="30"
            endColumn="31"
            endOffset="1101"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;添加评论...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/page_player_comments.xml"
            line="57"
            column="13"
            startOffset="1950"
            endLine="57"
            endColumn="35"
            endOffset="1972"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;发送&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/page_player_comments.xml"
            line="69"
            column="13"
            startOffset="2438"
            endLine="69"
            endColumn="30"
            endOffset="2455"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;播放列表&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/page_player_playlist.xml"
            line="17"
            column="13"
            startOffset="570"
            endLine="17"
            endColumn="32"
            endOffset="589"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;(0首)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/page_player_playlist.xml"
            line="30"
            column="13"
            startOffset="1090"
            endLine="30"
            endColumn="32"
            endOffset="1109"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;切换歌词&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_controls.xml"
            line="49"
            column="13"
            startOffset="1766"
            endLine="49"
            endColumn="46"
            endOffset="1799"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_controls.xml"
            line="67"
            column="13"
            startOffset="2361"
            endLine="67"
            endColumn="33"
            endOffset="2381"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_controls.xml"
            line="80"
            column="13"
            startOffset="2782"
            endLine="80"
            endColumn="33"
            endOffset="2802"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;上一首&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_controls.xml"
            line="97"
            column="13"
            startOffset="3353"
            endLine="97"
            endColumn="45"
            endOffset="3385"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;播放/暂停&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_controls.xml"
            line="110"
            column="13"
            startOffset="3882"
            endLine="110"
            endColumn="47"
            endOffset="3916"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;下一首&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_controls.xml"
            line="121"
            column="13"
            startOffset="4314"
            endLine="121"
            endColumn="45"
            endOffset="4346"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;加载中...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/view_lottie_loading.xml"
            line="25"
            column="9"
            startOffset="861"
            endLine="25"
            endColumn="30"
            endOffset="882"/>
    </incident>

    <incident
        id="RelativeOverlap"
        severity="warning"
        message="`@id/text_comment_count` can overlap `@id/button_comment_close` if @id/text_comment_count grows due to localized text expansion">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="27"
            column="10"
            startOffset="956"
            endLine="27"
            endColumn="18"
            endOffset="964"/>
    </incident>

    <incident
        id="RelativeOverlap"
        severity="warning"
        message="`@id/text_playlist_count` can overlap `@id/button_playlist_close` if @id/text_playlist_count grows due to localized text expansion">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_playlist.xml"
            line="23"
            column="10"
            startOffset="822"
            endLine="23"
            endColumn="18"
            endOffset="830"/>
    </incident>

</incidents>
