<lint-module
    format="1"
    dir="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app"
    name=":app"
    type="APP"
    maven="AI Music Player:app:unspecified"
    agpVersion="8.8.0"
    buildFolder="build"
    bootClassPath="D:\AndroidZhenSdk\platforms\android-34\android.jar;D:\AndroidZhenSdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
