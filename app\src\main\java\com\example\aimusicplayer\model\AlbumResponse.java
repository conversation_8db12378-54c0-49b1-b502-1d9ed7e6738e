package com.example.aimusicplayer.model;

import com.example.aimusicplayer.data.model.Song;
import com.example.aimusicplayer.data.model.Artist;
import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 专辑响应模型
 */
public class AlbumResponse {
    private int code;
    private Album album;
    private List<Song> songs;

    public int getCode() {
        return code;
    }

    public Album getAlbum() {
        return album;
    }

    public List<Song> getSongs() {
        return songs;
    }

    public static class Album {
        private long id;
        private String name;

        @SerializedName("picUrl")
        private String picUrl;

        private List<Artist> artists;
        private String description;

        public long getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public String getPicUrl() {
            return picUrl;
        }

        public List<Artist> getArtists() {
            return artists;
        }

        public String getDescription() {
            return description;
        }
    }
}
