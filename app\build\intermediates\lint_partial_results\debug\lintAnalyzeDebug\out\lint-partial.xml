<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.APPLICATION_DETAILS_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/PermissionUtils.kt"
                            line="126"
                            column="30"
                            startOffset="3794"
                            endLine="126"
                            endColumn="82"
                            endOffset="3846"/>
                    </map>
                    <map id="com.example.aimusicplayer.LOAD_NEW_SONGS (used to send a broadcast)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/service/UnifiedPlaybackService.kt"
                            line="237"
                            column="35"
                            startOffset="7953"
                            endLine="237"
                            endColumn="85"
                            endOffset="8003"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="android.content.BroadcastReceiver"
                    boolean="true"/>
                <entry
                    name="com.example.aimusicplayer.ui.splash.SplashActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.anim.button_press_feedback"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/button_press_feedback.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="16"
            endColumn="7"
            endOffset="553"/>
        <location id="R.anim.button_release_feedback"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/button_release_feedback.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="16"
            endColumn="7"
            endOffset="553"/>
        <location id="R.anim.comment_like_animation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/comment_like_animation.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="28"
            endColumn="7"
            endOffset="862"/>
        <location id="R.anim.comment_send_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/comment_send_success.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="39"
            endColumn="7"
            endOffset="1195"/>
        <location id="R.anim.fade_in"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/fade_in.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="74"
            endOffset="259"/>
        <location id="R.anim.fade_out"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/fade_out.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="74"
            endOffset="259"/>
        <location id="R.anim.item_animation_fall_down"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/item_animation_fall_down.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="24"
            endColumn="7"
            endOffset="701"/>
        <location id="R.anim.layout_animation_fall_down"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/layout_animation_fall_down.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="39"
            endOffset="236"/>
        <location id="R.anim.page_transition_fade"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/page_transition_fade.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="7"
            endOffset="438"/>
        <location id="R.anim.reset_rotation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/reset_rotation.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="7"
            endColumn="28"
            endOffset="237"/>
        <location id="R.anim.rotate_album"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/rotate_album.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="64"
            endOffset="339"/>
        <location id="R.anim.rotate_pause_to_play"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/rotate_pause_to_play.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="16"
            endColumn="7"
            endOffset="523"/>
        <location id="R.anim.rotate_play_to_pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/rotate_play_to_pause.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="16"
            endColumn="7"
            endOffset="522"/>
        <location id="R.anim.scale_down"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/scale_down.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="31"
            endOffset="298"/>
        <location id="R.anim.scale_up"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/scale_up.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="30"
            endOffset="298"/>
        <location id="R.color.accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="132"
            column="12"
            startOffset="5650"
            endLine="132"
            endColumn="25"
            endOffset="5663"/>
        <location id="R.color.accent_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="133"
            column="12"
            startOffset="5703"
            endLine="133"
            endColumn="30"
            endOffset="5721"/>
        <location id="R.color.accent_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="134"
            column="12"
            startOffset="5766"
            endLine="134"
            endColumn="31"
            endOffset="5785"/>
        <location id="R.color.background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="135"
            column="12"
            startOffset="5831"
            endLine="135"
            endColumn="29"
            endOffset="5848"/>
        <location id="R.color.blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="125"
            column="12"
            startOffset="5332"
            endLine="125"
            endColumn="23"
            endOffset="5343"/>
        <location id="R.color.button_text_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="151"
            column="12"
            startOffset="6685"
            endLine="151"
            endColumn="36"
            endOffset="6709"/>
        <location id="R.color.colorBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="168"
            column="12"
            startOffset="7481"
            endLine="168"
            endColumn="34"
            endOffset="7503"/>
        <location id="R.color.colorTextPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="169"
            column="12"
            startOffset="7547"
            endLine="169"
            endColumn="35"
            endOffset="7570"/>
        <location id="R.color.colorTextSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="170"
            column="12"
            startOffset="7610"
            endLine="170"
            endColumn="37"
            endOffset="7635"/>
        <location id="R.color.color_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="173"
            column="12"
            startOffset="7704"
            endLine="173"
            endColumn="35"
            endOffset="7727"/>
        <location id="R.color.color_blue_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="17"
            column="12"
            startOffset="731"
            endLine="17"
            endColumn="33"
            endOffset="752"/>
        <location id="R.color.color_blue_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="18"
            column="12"
            startOffset="780"
            endLine="18"
            endColumn="33"
            endOffset="801"/>
        <location id="R.color.color_blue_900"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="829"
            endLine="19"
            endColumn="33"
            endOffset="850"/>
        <location id="R.color.color_gray_100"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="290"
            endLine="8"
            endColumn="33"
            endOffset="311"/>
        <location id="R.color.color_gray_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="339"
            endLine="9"
            endColumn="33"
            endOffset="360"/>
        <location id="R.color.color_gray_400"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="11"
            column="12"
            startOffset="437"
            endLine="11"
            endColumn="33"
            endOffset="458"/>
        <location id="R.color.color_gray_50"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="242"
            endLine="7"
            endColumn="32"
            endOffset="262"/>
        <location id="R.color.color_gray_600"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="535"
            endLine="13"
            endColumn="33"
            endOffset="556"/>
        <location id="R.color.color_gray_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="584"
            endLine="14"
            endColumn="33"
            endOffset="605"/>
        <location id="R.color.color_gray_800"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="633"
            endLine="15"
            endColumn="33"
            endOffset="654"/>
        <location id="R.color.color_gray_900"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="682"
            endLine="16"
            endColumn="33"
            endOffset="703"/>
        <location id="R.color.color_green_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="1134"
            endLine="25"
            endColumn="34"
            endOffset="1156"/>
        <location id="R.color.color_light_blue_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="20"
            column="12"
            startOffset="878"
            endLine="20"
            endColumn="39"
            endOffset="905"/>
        <location id="R.color.color_light_blue_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="933"
            endLine="21"
            endColumn="39"
            endOffset="960"/>
        <location id="R.color.color_pink_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="988"
            endLine="22"
            endColumn="33"
            endOffset="1009"/>
        <location id="R.color.color_pink_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="1037"
            endLine="23"
            endColumn="33"
            endOffset="1058"/>
        <location id="R.color.color_red_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="1086"
            endLine="24"
            endColumn="32"
            endOffset="1106"/>
        <location id="R.color.color_transparent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="188"
            endLine="6"
            endColumn="36"
            endOffset="212"/>
        <location id="R.color.color_yellow_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="1184"
            endLine="26"
            endColumn="35"
            endOffset="1207"/>
        <location id="R.color.dark_gray"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="122"
            column="12"
            startOffset="5168"
            endLine="122"
            endColumn="28"
            endOffset="5184"/>
        <location id="R.color.divider"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="136"
            column="12"
            startOffset="5892"
            endLine="136"
            endColumn="26"
            endOffset="5906"/>
        <location id="R.color.divider_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="149"
            column="12"
            startOffset="6571"
            endLine="149"
            endColumn="32"
            endOffset="6591"/>
        <location id="R.color.driving_mode_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="97"
            column="12"
            startOffset="3990"
            endLine="97"
            endColumn="42"
            endOffset="4020"/>
        <location id="R.color.driving_mode_controls"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="99"
            column="12"
            startOffset="4100"
            endLine="99"
            endColumn="40"
            endOffset="4128"/>
        <location id="R.color.driving_mode_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="98"
            column="12"
            startOffset="4048"
            endLine="98"
            endColumn="36"
            endOffset="4072"/>
        <location id="R.color.error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="140"
            column="12"
            startOffset="6103"
            endLine="140"
            endColumn="24"
            endOffset="6115"/>
        <location id="R.color.gray"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="120"
            column="12"
            startOffset="5056"
            endLine="120"
            endColumn="23"
            endOffset="5067"/>
        <location id="R.color.green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="124"
            column="12"
            startOffset="5277"
            endLine="124"
            endColumn="24"
            endOffset="5289"/>
        <location id="R.color.info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="138"
            column="12"
            startOffset="5999"
            endLine="138"
            endColumn="23"
            endOffset="6010"/>
        <location id="R.color.light_gray"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="121"
            column="12"
            startOffset="5109"
            endLine="121"
            endColumn="29"
            endOffset="5126"/>
        <location id="R.color.list_item_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="93"
            column="12"
            startOffset="3850"
            endLine="93"
            endColumn="39"
            endOffset="3877"/>
        <location id="R.color.list_item_background_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="94"
            column="12"
            startOffset="3905"
            endLine="94"
            endColumn="48"
            endOffset="3941"/>
        <location id="R.color.lyric_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="88"
            column="12"
            startOffset="3682"
            endLine="88"
            endColumn="35"
            endOffset="3705"/>
        <location id="R.color.lyric_background_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="154"
            column="12"
            startOffset="6879"
            endLine="154"
            endColumn="41"
            endOffset="6908"/>
        <location id="R.color.lyric_highlight"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="89"
            column="12"
            startOffset="3733"
            endLine="89"
            endColumn="34"
            endOffset="3755"/>
        <location id="R.color.lyric_highlight_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="155"
            column="12"
            startOffset="6952"
            endLine="155"
            endColumn="40"
            endOffset="6980"/>
        <location id="R.color.lyric_normal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="90"
            column="12"
            startOffset="3783"
            endLine="90"
            endColumn="31"
            endOffset="3802"/>
        <location id="R.color.lyric_normal_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="156"
            column="12"
            startOffset="7023"
            endLine="156"
            endColumn="37"
            endOffset="7048"/>
        <location id="R.color.nav_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="68"
            column="12"
            startOffset="2859"
            endLine="68"
            endColumn="33"
            endOffset="2880"/>
        <location id="R.color.nav_icon_active"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="69"
            column="12"
            startOffset="2908"
            endLine="69"
            endColumn="34"
            endOffset="2930"/>
        <location id="R.color.nav_icon_inactive"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="70"
            column="12"
            startOffset="2958"
            endLine="70"
            endColumn="36"
            endOffset="2982"/>
        <location id="R.color.player_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="81"
            column="12"
            startOffset="3390"
            endLine="81"
            endColumn="36"
            endOffset="3414"/>
        <location id="R.color.player_control_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="85"
            column="12"
            startOffset="3603"
            endLine="85"
            endColumn="44"
            endOffset="3635"/>
        <location id="R.color.player_controls"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="82"
            column="12"
            startOffset="3442"
            endLine="82"
            endColumn="34"
            endOffset="3464"/>
        <location id="R.color.player_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="84"
            column="12"
            startOffset="3553"
            endLine="84"
            endColumn="34"
            endOffset="3575"/>
        <location id="R.color.player_progress_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="83"
            column="12"
            startOffset="3492"
            endLine="83"
            endColumn="45"
            endOffset="3525"/>
        <location id="R.color.primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="129"
            column="12"
            startOffset="5463"
            endLine="129"
            endColumn="26"
            endOffset="5477"/>
        <location id="R.color.primary_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="130"
            column="12"
            startOffset="5518"
            endLine="130"
            endColumn="31"
            endOffset="5537"/>
        <location id="R.color.primary_dark_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="144"
            column="12"
            startOffset="6240"
            endLine="144"
            endColumn="37"
            endOffset="6265"/>
        <location id="R.color.primary_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="131"
            column="12"
            startOffset="5583"
            endLine="131"
            endColumn="32"
            endOffset="5603"/>
        <location id="R.color.red"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="123"
            column="12"
            startOffset="5226"
            endLine="123"
            endColumn="22"
            endOffset="5236"/>
        <location id="R.color.ripple_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="152"
            column="12"
            startOffset="6751"
            endLine="152"
            endColumn="31"
            endOffset="6770"/>
        <location id="R.color.search_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="64"
            column="12"
            startOffset="2740"
            endLine="64"
            endColumn="36"
            endOffset="2764"/>
        <location id="R.color.search_bg_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="159"
            column="12"
            startOffset="7110"
            endLine="159"
            endColumn="34"
            endOffset="7132"/>
        <location id="R.color.search_stroke"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="65"
            column="12"
            startOffset="2792"
            endLine="65"
            endColumn="32"
            endOffset="2812"/>
        <location id="R.color.search_stroke_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="160"
            column="12"
            startOffset="7177"
            endLine="160"
            endColumn="38"
            endOffset="7203"/>
        <location id="R.color.sidebar_item_background_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="78"
            column="12"
            startOffset="3303"
            endLine="78"
            endColumn="51"
            endOffset="3342"/>
        <location id="R.color.sidebar_item_icon_normal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="76"
            column="12"
            startOffset="3183"
            endLine="76"
            endColumn="43"
            endOffset="3214"/>
        <location id="R.color.sidebar_item_icon_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="77"
            column="12"
            startOffset="3242"
            endLine="77"
            endColumn="45"
            endOffset="3275"/>
        <location id="R.color.sidebar_item_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="75"
            column="12"
            startOffset="3131"
            endLine="75"
            endColumn="36"
            endOffset="3155"/>
        <location id="R.color.success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="137"
            column="12"
            startOffset="5944"
            endLine="137"
            endColumn="26"
            endOffset="5958"/>
        <location id="R.color.text_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="53"
            column="12"
            startOffset="2372"
            endLine="53"
            endColumn="28"
            endOffset="2388"/>
        <location id="R.color.text_disabled"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="51"
            column="12"
            startOffset="2279"
            endLine="51"
            endColumn="32"
            endOffset="2299"/>
        <location id="R.color.text_link"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="54"
            column="12"
            startOffset="2416"
            endLine="54"
            endColumn="28"
            endOffset="2432"/>
        <location id="R.color.theme_accent_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="38"
            column="12"
            startOffset="1729"
            endLine="38"
            endColumn="36"
            endOffset="1753"/>
        <location id="R.color.theme_accent_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="39"
            column="12"
            startOffset="1781"
            endLine="39"
            endColumn="37"
            endOffset="1806"/>
        <location id="R.color.theme_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="42"
            column="12"
            startOffset="1933"
            endLine="42"
            endColumn="30"
            endOffset="1951"/>
        <location id="R.color.theme_info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="45"
            column="12"
            startOffset="2075"
            endLine="45"
            endColumn="29"
            endOffset="2092"/>
        <location id="R.color.theme_primary_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="36"
            column="12"
            startOffset="1628"
            endLine="36"
            endColumn="38"
            endOffset="1654"/>
        <location id="R.color.theme_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="43"
            column="12"
            startOffset="1979"
            endLine="43"
            endColumn="32"
            endOffset="1999"/>
        <location id="R.color.theme_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="41"
            column="12"
            startOffset="1885"
            endLine="41"
            endColumn="32"
            endOffset="1905"/>
        <location id="R.color.theme_warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="44"
            column="12"
            startOffset="2027"
            endLine="44"
            endColumn="32"
            endOffset="2047"/>
        <location id="R.color.transparent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="119"
            column="12"
            startOffset="4993"
            endLine="119"
            endColumn="30"
            endOffset="5011"/>
        <location id="R.color.ui_button_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="60"
            column="12"
            startOffset="2616"
            endLine="60"
            endColumn="33"
            endOffset="2637"/>
        <location id="R.color.ui_divider"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="57"
            column="12"
            startOffset="2481"
            endLine="57"
            endColumn="29"
            endOffset="2498"/>
        <location id="R.color.ui_ripple"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="58"
            column="12"
            startOffset="2526"
            endLine="58"
            endColumn="28"
            endOffset="2542"/>
        <location id="R.color.warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="139"
            column="12"
            startOffset="6048"
            endLine="139"
            endColumn="26"
            endOffset="6062"/>
        <location id="R.color.yellow"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="126"
            column="12"
            startOffset="5385"
            endLine="126"
            endColumn="25"
            endOffset="5398"/>
        <location id="R.dimen.corner_radius_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="39"
            column="12"
            startOffset="1983"
            endLine="39"
            endColumn="38"
            endOffset="2009"/>
        <location id="R.dimen.corner_radius_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="38"
            column="12"
            startOffset="1911"
            endLine="38"
            endColumn="39"
            endOffset="1938"/>
        <location id="R.dimen.corner_radius_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="37"
            column="12"
            startOffset="1840"
            endLine="37"
            endColumn="38"
            endOffset="1866"/>
        <location id="R.dimen.driving_control_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="73"
            column="12"
            startOffset="3835"
            endLine="73"
            endColumn="39"
            endOffset="3862"/>
        <location id="R.dimen.driving_seekbar_height"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="74"
            column="12"
            startOffset="3911"
            endLine="74"
            endColumn="41"
            endOffset="3940"/>
        <location id="R.dimen.driving_seekbar_thumb"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="75"
            column="12"
            startOffset="3988"
            endLine="75"
            endColumn="40"
            endOffset="4016"/>
        <location id="R.dimen.driving_text_size_body"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="13"
            column="12"
            startOffset="614"
            endLine="13"
            endColumn="41"
            endOffset="643"/>
        <location id="R.dimen.driving_text_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="14"
            column="12"
            startOffset="688"
            endLine="14"
            endColumn="42"
            endOffset="718"/>
        <location id="R.dimen.driving_text_size_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="12"
            column="12"
            startOffset="540"
            endLine="12"
            endColumn="42"
            endOffset="570"/>
        <location id="R.dimen.height_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="42"
            column="12"
            startOffset="2071"
            endLine="42"
            endColumn="32"
            endOffset="2091"/>
        <location id="R.dimen.height_card"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="48"
            column="12"
            startOffset="2508"
            endLine="48"
            endColumn="30"
            endOffset="2526"/>
        <location id="R.dimen.height_divider"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="45"
            column="12"
            startOffset="2289"
            endLine="45"
            endColumn="33"
            endOffset="2310"/>
        <location id="R.dimen.height_list_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="47"
            column="12"
            startOffset="2435"
            endLine="47"
            endColumn="35"
            endOffset="2458"/>
        <location id="R.dimen.height_navigation_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="44"
            column="12"
            startOffset="2216"
            endLine="44"
            endColumn="41"
            endOffset="2245"/>
        <location id="R.dimen.height_seekbar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="46"
            column="12"
            startOffset="2362"
            endLine="46"
            endColumn="33"
            endOffset="2383"/>
        <location id="R.dimen.height_toolbar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="43"
            column="12"
            startOffset="2143"
            endLine="43"
            endColumn="33"
            endOffset="2164"/>
        <location id="R.dimen.image_cover_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="62"
            column="12"
            startOffset="3277"
            endLine="62"
            endColumn="36"
            endOffset="3301"/>
        <location id="R.dimen.image_cover_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="61"
            column="12"
            startOffset="3206"
            endLine="61"
            endColumn="37"
            endOffset="3231"/>
        <location id="R.dimen.image_cover_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="60"
            column="12"
            startOffset="3135"
            endLine="60"
            endColumn="36"
            endOffset="3159"/>
        <location id="R.dimen.image_cover_xlarge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="63"
            column="12"
            startOffset="3348"
            endLine="63"
            endColumn="37"
            endOffset="3373"/>
        <location id="R.dimen.image_thumbnail_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="59"
            column="12"
            startOffset="3063"
            endLine="59"
            endColumn="40"
            endOffset="3091"/>
        <location id="R.dimen.image_thumbnail_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="58"
            column="12"
            startOffset="2991"
            endLine="58"
            endColumn="41"
            endOffset="3020"/>
        <location id="R.dimen.image_thumbnail_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="57"
            column="12"
            startOffset="2919"
            endLine="57"
            endColumn="40"
            endOffset="2947"/>
        <location id="R.dimen.margin_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="21"
            column="12"
            startOffset="1066"
            endLine="21"
            endColumn="31"
            endOffset="1085"/>
        <location id="R.dimen.margin_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="19"
            column="12"
            startOffset="922"
            endLine="19"
            endColumn="32"
            endOffset="942"/>
        <location id="R.dimen.margin_normal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="20"
            column="12"
            startOffset="994"
            endLine="20"
            endColumn="32"
            endOffset="1014"/>
        <location id="R.dimen.margin_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="18"
            column="12"
            startOffset="851"
            endLine="18"
            endColumn="31"
            endOffset="870"/>
        <location id="R.dimen.margin_tiny"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="17"
            column="12"
            startOffset="779"
            endLine="17"
            endColumn="30"
            endOffset="797"/>
        <location id="R.dimen.margin_xlarge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="22"
            column="12"
            startOffset="1137"
            endLine="22"
            endColumn="32"
            endOffset="1157"/>
        <location id="R.dimen.margin_xxlarge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="23"
            column="12"
            startOffset="1209"
            endLine="23"
            endColumn="33"
            endOffset="1230"/>
        <location id="R.dimen.padding_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="33"
            column="12"
            startOffset="1678"
            endLine="33"
            endColumn="32"
            endOffset="1698"/>
        <location id="R.dimen.padding_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="31"
            column="12"
            startOffset="1532"
            endLine="31"
            endColumn="33"
            endOffset="1553"/>
        <location id="R.dimen.padding_normal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="32"
            column="12"
            startOffset="1605"
            endLine="32"
            endColumn="33"
            endOffset="1626"/>
        <location id="R.dimen.padding_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="30"
            column="12"
            startOffset="1460"
            endLine="30"
            endColumn="32"
            endOffset="1480"/>
        <location id="R.dimen.padding_tiny"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="29"
            column="12"
            startOffset="1387"
            endLine="29"
            endColumn="31"
            endOffset="1406"/>
        <location id="R.dimen.padding_xlarge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="34"
            column="12"
            startOffset="1750"
            endLine="34"
            endColumn="33"
            endOffset="1771"/>
        <location id="R.dimen.player_control_size_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="68"
            column="12"
            startOffset="3586"
            endLine="68"
            endColumn="44"
            endOffset="3618"/>
        <location id="R.dimen.player_control_size_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="67"
            column="12"
            startOffset="3513"
            endLine="67"
            endColumn="45"
            endOffset="3546"/>
        <location id="R.dimen.player_control_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="66"
            column="12"
            startOffset="3440"
            endLine="66"
            endColumn="44"
            endOffset="3472"/>
        <location id="R.dimen.player_seekbar_height"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="69"
            column="12"
            startOffset="3667"
            endLine="69"
            endColumn="40"
            endOffset="3695"/>
        <location id="R.dimen.player_seekbar_thumb"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="70"
            column="12"
            startOffset="3740"
            endLine="70"
            endColumn="39"
            endOffset="3767"/>
        <location id="R.dimen.text_size_body"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="7"
            column="12"
            startOffset="292"
            endLine="7"
            endColumn="33"
            endOffset="313"/>
        <location id="R.dimen.text_size_caption"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="8"
            column="12"
            startOffset="362"
            endLine="8"
            endColumn="36"
            endOffset="386"/>
        <location id="R.dimen.text_size_headline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="12"
            startOffset="80"
            endLine="4"
            endColumn="37"
            endOffset="105"/>
        <location id="R.dimen.text_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="9"
            column="12"
            startOffset="434"
            endLine="9"
            endColumn="34"
            endOffset="456"/>
        <location id="R.dimen.text_size_subtitle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="6"
            column="12"
            startOffset="221"
            endLine="6"
            endColumn="37"
            endOffset="246"/>
        <location id="R.dimen.text_size_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="5"
            column="12"
            startOffset="151"
            endLine="5"
            endColumn="34"
            endOffset="173"/>
        <location id="R.dimen.width_fab"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="53"
            column="12"
            startOffset="2742"
            endLine="53"
            endColumn="28"
            endOffset="2758"/>
        <location id="R.dimen.width_icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="52"
            column="12"
            startOffset="2670"
            endLine="52"
            endColumn="29"
            endOffset="2687"/>
        <location id="R.dimen.width_sidebar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="51"
            column="12"
            startOffset="2597"
            endLine="51"
            endColumn="32"
            endOffset="2617"/>
        <location id="R.dimen.width_sidebar_selection_indicator"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="54"
            column="12"
            startOffset="2816"
            endLine="54"
            endColumn="52"
            endOffset="2856"/>
        <location id="R.drawable.bg_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_button.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="357"/>
        <location id="R.drawable.bg_comment_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_comment_success.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="9"
            endOffset="348"/>
        <location id="R.drawable.bg_edit_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_edit_text.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="9"
            endOffset="219"/>
        <location id="R.drawable.bg_playing_cover_border"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_playing_cover_border.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="9"
            endOffset="179"/>
        <location id="R.drawable.bg_playing_playback_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_playing_playback_progress.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="17"
            endColumn="14"
            endOffset="537"/>
        <location id="R.drawable.button_playlist_close"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_playlist_close.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="407"/>
        <location id="R.drawable.button_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_secondary.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="25"
            endColumn="10"
            endOffset="752"/>
        <location id="R.drawable.cherry_blossom_car_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/cherry_blossom_car_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="74"
            endColumn="10"
            endOffset="4939"/>
        <location id="R.drawable.control_button_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/control_button_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="9"
            endOffset="177"/>
        <location id="R.drawable.custom_app_icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/custom_app_icon.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="41"
            endColumn="10"
            endOffset="1362"/>
        <location id="R.drawable.dark_blue_gradient_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dark_blue_gradient_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="9"
            endOffset="329"/>
        <location id="R.drawable.dialog_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dialog_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="24"
            endColumn="9"
            endOffset="617"/>
        <location id="R.drawable.edit_text_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/edit_text_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="299"/>
        <location id="R.drawable.ic_arrow_down"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_down.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="366"/>
        <location id="R.drawable.ic_arrow_left"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_left.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="544"/>
        <location id="R.drawable.ic_arrow_right"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_right.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="366"/>
        <location id="R.drawable.ic_car"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_car.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="696"/>
        <location id="R.drawable.ic_default_cover"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_default_cover.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="17"
            endColumn="10"
            endOffset="603"/>
        <location id="R.drawable.ic_discovery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_discovery.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="523"/>
        <location id="R.drawable.ic_driving"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_driving.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="707"/>
        <location id="R.drawable.ic_equalizer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_equalizer.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="367"/>
        <location id="R.drawable.ic_globe"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_globe.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="668"/>
        <location id="R.drawable.ic_grid"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_grid.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="391"/>
        <location id="R.drawable.ic_heart"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_heart.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="496"/>
        <location id="R.drawable.ic_heart_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_heart_mode.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="558"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="170"
            endColumn="10"
            endOffset="5605"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v24/ic_launcher_foreground.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="1702"/>
        <location id="R.drawable.ic_library"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_library.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="567"/>
        <location id="R.drawable.ic_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_menu.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="363"/>
        <location id="R.drawable.ic_music"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_music.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="418"/>
        <location id="R.drawable.ic_music_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_music_note.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="368"/>
        <location id="R.drawable.ic_needle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_needle.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="22"
            endColumn="10"
            endOffset="626"/>
        <location id="R.drawable.ic_play_mode_level_list"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_mode_level_list.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="14"
            endOffset="420"/>
        <location id="R.drawable.ic_play_mode_loop"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_mode_loop.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="380"/>
        <location id="R.drawable.ic_play_mode_shuffle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_mode_shuffle.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="492"/>
        <location id="R.drawable.ic_play_mode_single"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_mode_single.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="414"/>
        <location id="R.drawable.ic_play_order"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_order.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="570"/>
        <location id="R.drawable.ic_play_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_small.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="320"/>
        <location id="R.drawable.ic_player"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_player.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="324"/>
        <location id="R.drawable.ic_playing_play_pause_selector"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_playing_play_pause_selector.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="12"
            endOffset="251"/>
        <location id="R.drawable.ic_playing_playback_progress_thumb"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_playing_playback_progress_thumb.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="9"
            endOffset="249"/>
        <location id="R.drawable.ic_profile"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="444"/>
        <location id="R.drawable.ic_random"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_random.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="495"/>
        <location id="R.drawable.ic_refresh"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="10"
            endOffset="581"/>
        <location id="R.drawable.ic_repeat_all"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_repeat_all.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="383"/>
        <location id="R.drawable.ic_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="1216"/>
        <location id="R.drawable.ic_user"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_user.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="444"/>
        <location id="R.drawable.ic_vinyl_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_vinyl_bg.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="31"
            endColumn="14"
            endOffset="1035"/>
        <location id="R.drawable.login_button_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/login_button_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="7"
            endColumn="9"
            endOffset="290"/>
        <location id="R.drawable.logo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.jpg"/>
        <location id="R.drawable.next"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/next.png"/>
        <location id="R.drawable.pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/pause.png"/>
        <location id="R.drawable.play"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/play.png"/>
        <location id="R.drawable.player_control_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/player_control_bg.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="9"
            endOffset="336"/>
        <location id="R.drawable.previous"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/previous.png"/>
        <location id="R.drawable.round_menu_button_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/round_menu_button_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="9"
            endOffset="243"/>
        <location id="R.drawable.search_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="9"
            endOffset="337"/>
        <location id="R.drawable.sidebar_sakura_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/sidebar_sakura_bg.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="9"
            endOffset="344"/>
        <location id="R.drawable.splash_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/splash_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="17"
            endOffset="397"/>
        <location id="R.integer.anim_duration_long"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="80"
            column="14"
            startOffset="4237"
            endLine="80"
            endColumn="39"
            endOffset="4262"/>
        <location id="R.integer.anim_duration_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="79"
            column="14"
            startOffset="4165"
            endLine="79"
            endColumn="41"
            endOffset="4192"/>
        <location id="R.integer.anim_duration_short"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="78"
            column="14"
            startOffset="4094"
            endLine="78"
            endColumn="40"
            endOffset="4120"/>
        <location id="R.layout.activity_player"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="208"
            endColumn="15"
            endOffset="8510"/>
        <location id="R.layout.dialog_comment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comment.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="153"
            endColumn="16"
            endOffset="5605"/>
        <location id="R.layout.dialog_heart_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_heart_mode.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="115"
            endColumn="16"
            endOffset="4070"/>
        <location id="R.layout.dialog_intelligence"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_intelligence.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="75"
            endColumn="16"
            endOffset="2722"/>
        <location id="R.layout.item_comment_header"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_comment_header.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="16"
            endOffset="658"/>
        <location id="R.layout.player_controls"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_controls.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="126"
            endColumn="16"
            endOffset="4514"/>
        <location id="R.layout.view_album_cover"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/view_album_cover.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="35"
            endColumn="15"
            endOffset="1107"/>
        <location id="R.mipmap.cherry_blossom_car"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/cherry_blossom_car.webp"/>
        <location id="R.mipmap.cherry_blossom_car_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/cherry_blossom_car_foreground.webp"/>
        <location id="R.mipmap.cherry_blossom_car_round"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/cherry_blossom_car_round.webp"/>
        <location id="R.string.action_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="112"
            endLine="4"
            endColumn="35"
            endOffset="134"/>
        <location id="R.string.app_logo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="518"
            endLine="14"
            endColumn="28"
            endOffset="533"/>
        <location id="R.string.app_slogan"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="567"
            endLine="15"
            endColumn="30"
            endOffset="584"/>
        <location id="R.string.cancel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1089"
            endLine="26"
            endColumn="26"
            endOffset="1102"/>
        <location id="R.string.cancel_retry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="63"
            column="13"
            startOffset="2595"
            endLine="63"
            endColumn="32"
            endOffset="2614"/>
        <location id="R.string.comment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="47"
            column="13"
            startOffset="1912"
            endLine="47"
            endColumn="27"
            endOffset="1926"/>
        <location id="R.string.copyright"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="439"
            endLine="11"
            endColumn="29"
            endOffset="455"/>
        <location id="R.string.favorite"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="1784"
            endLine="44"
            endColumn="28"
            endOffset="1799"/>
        <location id="R.string.guest_login"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="712"
            endLine="18"
            endColumn="31"
            endOffset="730"/>
        <location id="R.string.heart_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="1868"
            endLine="46"
            endColumn="30"
            endOffset="1885"/>
        <location id="R.string.intelligence_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="78"
            column="13"
            startOffset="3154"
            endLine="78"
            endColumn="37"
            endOffset="3178"/>
        <location id="R.string.intelligence_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="77"
            column="13"
            startOffset="3103"
            endLine="77"
            endColumn="37"
            endOffset="3127"/>
        <location id="R.string.intelligence_recommendation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="79"
            column="13"
            startOffset="3214"
            endLine="79"
            endColumn="47"
            endOffset="3248"/>
        <location id="R.string.loading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1357"
            endLine="31"
            endColumn="27"
            endOffset="1371"/>
        <location id="R.string.login"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1052"
            endLine="25"
            endColumn="25"
            endOffset="1064"/>
        <location id="R.string.login_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="858"
            endLine="21"
            endColumn="32"
            endOffset="877"/>
        <location id="R.string.login_successful"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="808"
            endLine="20"
            endColumn="36"
            endOffset="831"/>
        <location id="R.string.network_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="904"
            endLine="22"
            endColumn="33"
            endOffset="924"/>
        <location id="R.string.network_restored"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="65"
            column="13"
            startOffset="2693"
            endLine="65"
            endColumn="36"
            endOffset="2716"/>
        <location id="R.string.network_unavailable"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="64"
            column="13"
            startOffset="2639"
            endLine="64"
            endColumn="39"
            endOffset="2665"/>
        <location id="R.string.next"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="1664"
            endLine="41"
            endColumn="24"
            endOffset="1675"/>
        <location id="R.string.no_intelligence_songs"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="80"
            column="13"
            startOffset="3275"
            endLine="80"
            endColumn="41"
            endOffset="3303"/>
        <location id="R.string.no_lyrics"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="223"
            endLine="6"
            endColumn="29"
            endOffset="239"/>
        <location id="R.string.password_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1004"
            endLine="24"
            endColumn="33"
            endOffset="1024"/>
        <location id="R.string.pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="1627"
            endLine="40"
            endColumn="25"
            endOffset="1639"/>
        <location id="R.string.phone_login"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="17"
            column="13"
            startOffset="666"
            endLine="17"
            endColumn="31"
            endOffset="684"/>
        <location id="R.string.phone_number_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="951"
            endLine="23"
            endColumn="37"
            endOffset="975"/>
        <location id="R.string.play"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="1591"
            endLine="39"
            endColumn="24"
            endOffset="1602"/>
        <location id="R.string.playlist"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="1742"
            endLine="43"
            endColumn="28"
            endOffset="1757"/>
        <location id="R.string.previous"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="1701"
            endLine="42"
            endColumn="28"
            endOffset="1716"/>
        <location id="R.string.qr_code_expired"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1241"
            endLine="29"
            endColumn="35"
            endOffset="1263"/>
        <location id="R.string.qr_code_login"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="619"
            endLine="16"
            endColumn="33"
            endOffset="639"/>
        <location id="R.string.qr_code_scanned"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1298"
            endLine="30"
            endColumn="35"
            endOffset="1320"/>
        <location id="R.string.recommend"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="354"
            endLine="9"
            endColumn="29"
            endOffset="370"/>
        <location id="R.string.refresh_qr_code"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1127"
            endLine="27"
            endColumn="35"
            endOffset="1149"/>
        <location id="R.string.retry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="62"
            column="13"
            startOffset="2558"
            endLine="62"
            endColumn="25"
            endOffset="2570"/>
        <location id="R.string.scan_qr_code_tip"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1177"
            endLine="28"
            endColumn="36"
            endOffset="1200"/>
        <location id="R.string.search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="316"
            endLine="8"
            endColumn="26"
            endOffset="329"/>
        <location id="R.string.search_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="266"
            endLine="7"
            endColumn="31"
            endOffset="284"/>
        <location id="R.string.setting_auto_play"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="1970"
            endLine="50"
            endColumn="37"
            endOffset="1994"/>
        <location id="R.string.setting_auto_voice_in_driving"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="52"
            column="13"
            startOffset="2073"
            endLine="52"
            endColumn="49"
            endOffset="2109"/>
        <location id="R.string.setting_night_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="2021"
            endLine="51"
            endColumn="38"
            endOffset="2046"/>
        <location id="R.string.toplist"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="397"
            endLine="10"
            endColumn="27"
            endOffset="411"/>
        <location id="R.string.unfavorite"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="1824"
            endLine="45"
            endColumn="30"
            endOffset="1841"/>
        <location id="R.string.version_info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="757"
            endLine="19"
            endColumn="32"
            endOffset="776"/>
        <location id="R.string.welcome_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="165"
            endLine="5"
            endColumn="35"
            endOffset="187"/>
        <location id="R.style.AppButton"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="4"
            column="12"
            startOffset="80"
            endLine="4"
            endColumn="28"
            endOffset="96"/>
        <location id="R.style.LyricTextStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="45"
            column="12"
            startOffset="1879"
            endLine="45"
            endColumn="33"
            endOffset="1900"/>
        <location id="R.style.PlayPauseButton"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="37"
            column="12"
            startOffset="1554"
            endLine="37"
            endColumn="34"
            endOffset="1576"/>
        <location id="R.style.PlayerControlButton"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="28"
            column="12"
            startOffset="1171"
            endLine="28"
            endColumn="38"
            endOffset="1197"/>
        <location id="R.style.SearchEditText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="16"
            column="12"
            startOffset="609"
            endLine="16"
            endColumn="33"
            endOffset="630"/>
        <location id="R.style.SplashTheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/splash_theme.xml"
            line="4"
            column="12"
            startOffset="84"
            endLine="4"
            endColumn="30"
            endOffset="102"/>
        <entry
            name="model"
            string="anim[button_press(U),button_press_feedback(D),button_release_feedback(D),comment_like_animation(D),comment_send_success(D),fade_in(D),fade_out(D),item_animation_comment_fade_in(U),item_animation_comment_slide_up(U),item_animation_fall_down(D),item_animation_from_bottom(U),item_animation_from_right(U),layout_animation_comment_list(U),layout_animation_fall_down(D),page_transition_fade(D),reset_rotation(D),rotate(U),rotate_album(D),rotate_pause_to_play(D),rotate_play_to_pause(D),scale_down(D),scale_up(D),shake(U),slide_in_down(U),slide_in_left(U),slide_in_right(U),slide_in_up(U),slide_out_down(U),slide_out_left(U),slide_out_right(U),slide_out_up(U),slide_up(U)],attr[colorControlNormal(R),actionBarSize(R),selectableItemBackgroundBorderless(R),colorBackground(D),textColorPrimary(U),textColorSecondary(U),colorAccent(R),selectableItemBackground(R),colorSurface(R),lottieAnimationAsset(D),loadingMessage(D),autoPlay(D),loop(D),colorPrimaryVariant(R)],color[theme_accent(U),colorPrimary(U),colorAccent(U),button_color(U),primary_color(U),accent_color(U),sakura_primary_dark(U),sakura_primary(U),sakura_background(U),sakura_divider(U),search_bg_color(D),search_stroke_color(D),color_black(U),text_light(U),sidebar_background(U),nav_indicator(U),splash_background(U),sakura_text_primary(U),sakura_text_secondary(U),sakura_accent(U),text_primary(U),text_secondary(U),color_gray_500(U),color_white(U),background_dark(U),background_dark_lighter(U),white(U),text_hint(U),background_color(U),toolbar_background(U),card_background(U),color_gray_300(U),black(U),text_primary_color(U),text_secondary_color(U),color_transparent(D),color_gray_50(D),color_gray_100(D),color_gray_200(D),color_gray_400(D),color_gray_600(D),color_gray_700(D),color_gray_800(D),color_gray_900(D),color_blue_500(D),color_blue_700(D),color_blue_900(D),color_light_blue_500(D),color_light_blue_700(D),color_pink_500(D),color_pink_700(D),color_red_500(D),color_green_500(D),color_yellow_500(D),color_purple_200(U),color_purple_500(U),color_purple_700(U),color_teal_200(U),color_teal_700(U),theme_primary(U),theme_primary_dark(U),theme_primary_light(D),theme_accent_dark(D),theme_accent_light(D),theme_background(U),theme_surface(D),theme_error(D),theme_success(D),theme_warning(D),theme_info(D),text_disabled(D),text_dark(D),text_link(D),ui_divider(D),ui_ripple(D),ui_button(U),ui_button_text(D),ui_splash_background(U),search_background(D),search_stroke(D),nav_background(D),nav_icon_active(D),nav_icon_inactive(D),sidebar_item_text(D),sidebar_item_icon_normal(D),sidebar_item_icon_selected(D),sidebar_item_background_selected(D),player_background(D),player_controls(D),player_progress_background(D),player_progress(D),player_control_background(D),lyric_background(D),lyric_highlight(D),lyric_normal(D),list_item_background(D),list_item_background_selected(D),driving_mode_background(D),driving_mode_text(D),driving_mode_controls(D),purple_200(U),purple_500(U),purple_700(U),teal_200(U),teal_700(U),transparent(D),gray(D),light_gray(D),dark_gray(D),red(D),green(D),blue(D),yellow(D),primary(D),primary_dark(D),primary_light(D),accent(D),accent_dark(D),accent_light(D),background(D),divider(D),success(D),info(D),warning(D),error(D),primary_dark_color(D),divider_color(D),button_text_color(D),ripple_color(D),lyric_background_color(D),lyric_highlight_color(D),lyric_normal_color(D),colorPrimaryDark(U),colorBackground(D),colorTextPrimary(D),colorTextSecondary(D),color_background(D)],dimen[text_size_headline(D),text_size_title(D),text_size_subtitle(D),text_size_body(D),text_size_caption(D),text_size_small(D),driving_text_size_title(D),driving_text_size_body(D),driving_text_size_small(D),margin_tiny(D),margin_small(D),margin_medium(D),margin_normal(D),margin_large(D),margin_xlarge(D),margin_xxlarge(D),spacing_large(U),padding_tiny(D),padding_small(D),padding_medium(D),padding_normal(D),padding_large(D),padding_xlarge(D),corner_radius_small(D),corner_radius_medium(D),corner_radius_large(D),height_button(D),height_toolbar(D),height_navigation_item(D),height_divider(D),height_seekbar(D),height_list_item(D),height_card(D),width_sidebar(D),width_icon(D),width_fab(D),width_sidebar_selection_indicator(D),image_thumbnail_small(D),image_thumbnail_medium(D),image_thumbnail_large(D),image_cover_small(D),image_cover_medium(D),image_cover_large(D),image_cover_xlarge(D),player_control_size_small(D),player_control_size_medium(D),player_control_size_large(D),player_seekbar_height(D),player_seekbar_thumb(D),driving_control_size(D),driving_seekbar_height(D),driving_seekbar_thumb(D)],drawable[album_art_border(U),back_button_background(U),bg_bottom_sheet(U),bg_button(D),bg_button_primary(U),bg_comment_input(U),bg_comment_success(D),bg_edit_text(D),bg_playing_cover_border(D),bg_playing_disc(U),bg_playing_playback_progress(D),bg_vip_tag(U),button_background(U),button_playlist_close(D),button_primary(U),button_rounded(U),button_secondary(D),cherry_blossom_car(U),cherry_blossom_car_background(D),circle_background(U),circle_background_top3(U),comment_input_background(U),control_button_background(D),custom_app_icon(D),dark_blue_gradient_background(D),default_album_art(U),default_avatar(U),default_background(U),default_cover(U),dialog_background(D),edit_text_background(D),gradient_blue_purple(U),gradient_overlay(U),ic_arrow_back(U),ic_arrow_down(D),ic_arrow_left(D),ic_arrow_right(D),ic_back(U),ic_back_improved(U),ic_car(D),ic_check_circle(U),ic_close(U),ic_comment(U),ic_default_cover(D),ic_discovery(D),ic_driving(D),ic_equalizer(D),ic_favorite(U),ic_favorite_border(U),ic_globe(D),ic_grid(D),ic_guest_login(U),ic_heart(D),ic_heart_mode(D),ic_intelligence(U),ic_launcher_background(D),ic_library(D),ic_like_filled(U),ic_like_outline(U),ic_menu(D),ic_more_vert(U),ic_music(D),ic_music_note(D),ic_needle(D),ic_next(U),ic_notification(U),ic_pause(U),ic_phone_login(U),ic_play(U),ic_play_mode_level_list(D),ic_play_mode_loop(D),ic_play_mode_shuffle(D),ic_play_mode_single(D),ic_play_order(D),ic_play_small(D),ic_player(D),ic_playing_needle(U),ic_playing_play_pause_selector(D),ic_playing_playback_progress_thumb(D),ic_playlist(U),ic_previous(U),ic_profile(D),ic_qr_error(U),ic_qr_placeholder(U),ic_qrcode_login(U),ic_random(D),ic_refresh(D),ic_repeat(U),ic_repeat_all(D),ic_repeat_one(U),ic_settings(D),ic_share(U),ic_shuffle(U),ic_user(D),ic_vinyl_bg(D),icon_car(U),icon_globe(U),icon_heart(U),icon_music(U),icon_settings(U),icon_user(U),login_button_background(D),logo(D),logo_music(U),modern_login_button(U),nav_button_background(U),next(D),pause(D),play(D),player_control_bg(D),previous(D),ripple_circular_button(U),round_button_background(U),ripple_oval_button(U),round_icon_bg(U),round_menu_button_background(D),rounded_status_background(U),sakura_button(U),sakura_button_secondary(U),sakura_dialog_background(U),sakura_edit_background(U),search_background(D),sidebar_sakura_bg(D),splash_background(D),splash_gradient(U),vinyl_arm(U),vinyl_border(U),vinyl_center(U),vinyl_record(U),ic_launcher_foreground(D),ic_launcher_foreground_1(E)],id[login_root_layout(D),app_logo(U),tv_welcome(U),tv_subtitle(U),login_buttons_container(D),btn_qrcode_login(D),btn_phone_login(D),btn_guest_login(D),progress_bar(U),tv_status(U),nav_host_fragment(U),fragment_container(D),loading_view(D),sidebar_nav(U),nav_player(U),nav_player_indicator(D),nav_library(U),nav_library_indicator(D),nav_discovery(U),nav_discovery_indicator(D),nav_driving(U),nav_driving_indicator(D),nav_profile(U),nav_profile_indicator(D),nav_settings(U),nav_settings_indicator(D),btn_menu_right(D),iv_background(D),btn_back(D),tv_title(U),tv_artist(D),album_cover_view(D),sv_lyrics(D),tv_lyrics(D),tv_current_time(D),seekbar_progress(D),tv_total_time(D),btn_play_mode(D),btn_prev(D),btn_play_pause(D),btn_next(D),btn_playlist(D),splashImageView(U),app_name_text(U),welcome_text(U),text_comment_title(U),text_comment_count(D),button_comment_close(D),loading_view_comment(D),text_empty_comment(D),swipe_refresh_comment(D),recycler_view_comment(D),layout_load_more(D),edit_text_comment(D),btn_send_comment(U),text_comment_success(D),button_heart_mode_refresh(D),button_heart_mode_close(D),recycler_view_heart_mode(D),loading_view_heart_mode(D),btn_start_heart_mode(D),text_intelligence_title(D),btn_close_intelligence(D),loading_view_intelligence(D),text_empty_intelligence(D),recycler_view_intelligence(D),tv_switch_login_method(D),tv_phone_label(D),et_phone(U),password_login_container(D),tv_password_label(D),et_password(U),captcha_login_container(D),tv_captcha_label(D),et_captcha(U),btn_get_captcha(D),btn_cancel(U),btn_login(U),text_playlist_count(U),text_playlist_title(U),button_playlist_close(U),recycler_view_playlist(U),text_empty_playlist(U),btn_clear_playlist(D),qr_container(U),qr_image(U),qr_loading(U),qr_error_container(U),qr_error_icon(D),qr_error_text(U),btn_reload_qr(U),qr_status(U),tv_tip(U),layout_title_bar(U),swipe_refresh_layout(D),recycler_view_comments(D),load_more_progress(D),layout_comment_input(D),edit_comment(U),header_container(U),time_text(D),date_text(D),album_art(D),loading_progress(D),song_title(D),artist_text(D),seek_bar(U),current_time(D),total_time(D),previous_button(D),play_pause_button(D),next_button(D),voice_control_container(D),voice_button(D),exit_container(D),exit_button(D),toolbar(U),card_current_song(U),iv_album_cover(U),tv_song_title(U),tv_intelligence_desc(D),rv_intelligence_songs(D),tv_empty(D),text_placeholder(U),background_blur(D),background_overlay(D),content_container(D),control_container(U),vinyl_container(U),vinyl_border(D),vinyl_arm(D),vinyl_background(D),vinyl_center(D),song_artist(D),tab_layout_player(D),view_pager_player(D),textview_player_current_time(D),seekbar_player_progress(D),textview_player_total_time(D),button_player_collect(D),button_player_prev(D),button_player_play_pause(D),button_player_next(D),button_player_play_mode(D),button_player_playlist(D),button_player_intelligence(D),button_player_comment(D),button_player_share(D),recyclerView(U),progressBar(U),appbar_layout(D),collapsing_toolbar(D),iv_user_cover(D),view_gradient_overlay(D),iv_user_avatar(U),layout_user_info(D),tv_username(D),layout_user_tags(D),tv_vip_tag(D),tv_level_tag(D),tv_signature(D),card_user_stats(U),tv_liked_songs_count(D),tv_playlists_count(D),tv_followed_artists_count(D),tv_listening_hours(D),tv_more(D),tv_phone(D),tv_email(D),tv_vip_status(D),tv_register_time(D),btn_logout(D),tv_error(D),image_comment_avatar(U),text_comment_username(U),text_comment_time(U),text_comment_content(U),layout_like(U),image_like(D),text_like_count(D),layout_reply(D),text_reply_count(D),layout_more(D),recycler_view_replies(D),text_comment_header(D),rankTextView(U),searchWordTextView(U),scoreTextView(U),item_song_name(U),item_vip_tag(U),item_artist_name(U),item_album_name(U),ivPlaylistCover(U),tvPlaylistName(U),tvPlaylistDescription(U),tvUpdateFrequency(U),image_song_cover(U),text_song_title(U),text_song_artist(U),image_avatar(U),text_username(U),text_reply_time(U),text_reply_content(U),layout_reply_like(D),image_reply_like(D),text_reply_like_count(D),text_reply_to_reply(D),typeIconView(U),suggestTitleTextView(U),suggestSubTextView(U),text_song_duration(U),text_vip_tag(D),topListCoverImageView(U),topListNameTextView(U),topListDescTextView(U),topListSongCountTextView(U),lyrics_view(D),songNameTextView(D),artistTextView(D),toggleLyricButton(D),currentTimeTextView(D),seekBar(D),totalTimeTextView(D),previousButton(D),playPauseButton(D),nextButton(D),iv_vinyl(D),iv_needle(D),lottie_animation_view(U),loading_message(U),nav_graph(U),mainFragment(U),action_mainFragment_to_playerFragment(R),playerFragment(U),action_mainFragment_to_settingsFragment(R),settingsFragment(U),action_playerFragment_to_playlistFragment(R),playlistFragment(U),action_playerFragment_to_commentFragment(R),commentFragment(U),action_playerFragment_to_intelligenceFragment(R),intelligenceFragment(U),musicLibraryFragment(U),action_musicLibraryFragment_to_playerFragment(R),action_musicLibraryFragment_to_playlistDetailFragment(R),playlistDetailFragment(U),discoveryFragment(U),action_discoveryFragment_to_playerFragment(R),action_discoveryFragment_to_searchFragment(R),searchFragment(U),drivingModeFragment(U),action_commentFragment_to_playerFragment(R),action_intelligenceFragment_to_playerFragment(R),action_searchFragment_to_playerFragment(R),action_playlistDetailFragment_to_playerFragment(R),loginFragment(U),action_loginFragment_to_mainFragment(R),design_bottom_sheet(R)],integer[anim_duration_short(D),anim_duration_medium(D),anim_duration_long(D)],layout[activity_login(U),activity_main(U),activity_player(D),activity_splash(U),dialog_comment(D),item_comment(U),dialog_heart_mode(D),item_playlist_song(U),dialog_intelligence(D),dialog_phone_login(U),dialog_playlist(U),item_playlist(U),dialog_qr_login(U),fragment_comment(U),fragment_driving_mode(U),fragment_intelligence(U),item_song(U),fragment_placeholder(U),fragment_player(U),fragment_top_list(U),fragment_user_profile(U),item_reply(U),item_comment_header(D),item_hot_search(U),item_online_song(U),item_search_suggest(U),item_top_list(U),page_player_comments(U),page_player_lyrics(U),page_player_playlist(U),player_controls(D),view_album_cover(D),view_lottie_loading(U),fragment_main(R),fragment_music_library(R),fragment_discovery(R),fragment_settings(R),fragment_playlist(R),fragment_search(R),fragment_playlist_detail(R),fragment_login(R)],mipmap[ic_launcher(U),cherry_blossom_car(D),cherry_blossom_car_foreground(D),cherry_blossom_car_round(D)],navigation[nav_graph(U)],string[cancel(D),no_lyrics(D),play_mode_loop(U),previous(D),play(D),next(D),playlist(D),app_name(U),back(U),no_comments(U),comment_hint(U),send(U),appbar_scrolling_view_behavior(R),user_avatar(U),album_cover(U),vip(U),action_settings(D),welcome_message(D),search_hint(D),search(D),recommend(D),toplist(D),copyright(D),app_logo(D),app_slogan(D),qr_code_login(D),phone_login(D),guest_login(D),version_info(D),login_successful(D),login_failed(D),network_error(D),phone_number_hint(D),password_hint(D),login(D),refresh_qr_code(D),scan_qr_code_tip(D),qr_code_expired(D),qr_code_scanned(D),loading(D),play_mode_shuffle(U),play_mode_single(U),pause(D),favorite(D),unfavorite(D),heart_mode(D),comment(D),setting_auto_play(D),setting_night_mode(D),setting_auto_voice_in_driving(D),error_timeout(U),error_no_network(U),error_network(U),error_auth(U),error_client(U),error_server(U),error_unknown(U),retry(D),cancel_retry(D),network_unavailable(D),network_restored(D),comment_title(U),comment_empty_hint(U),intelligence_mode(D),intelligence_hint(D),intelligence_recommendation(D),no_intelligence_songs(D)],style[Theme_AIMusicPlayer(U),FullScreenTheme(U),SplashTheme(D),Theme_AppCompat_NoActionBar(E),AppButton(D),SearchEditText(D),PlayerControlButton(D),PlayPauseButton(D),LyricTextStyle(D),BottomSheetDialogTheme(U),Theme_MaterialComponents_DayNight_BottomSheetDialog(R),BottomSheetStyle(U),Widget_MaterialComponents_BottomSheet_Modal(R),Theme_MaterialComponents_DayNight_DarkActionBar(R),Widget_MaterialComponents_TextInputLayout_OutlinedBox(R),Theme_MaterialComponents_NoActionBar(R)],styleable[LottieLoadingView(U),LottieLoadingView_lottieAnimationAsset(R),LottieLoadingView_loadingMessage(R),LottieLoadingView_autoPlay(R),LottieLoadingView_loop(R)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U),file_paths(U)];c^8,d^9,2f^69,30^2e,31^79,32^69,33^2e,38^7c,39^7d,3e^7b,48^45,4a^6e,4e^3a,4f^42,50^43,92^64,93^65,94^66,95^67,96^68,97^51,98^44,99^4d,9a^57,9b^61,9c^62,9d^5a,9e^63,9f^69,a0^6a,a1^6b,a2^2e,a3^6c,a4^6d,a5^6e,a6^77,a7^71,a8^73,a9^72,aa^70,ab^6a,ac^77,ad^7a,ae^78,af^8a,b0^8b,b1^8c,b2^6a,b3^6e,b4^42,b5^43,b6^6e,ee^2e,ef^2f,f6^30,f7^31,fa^2f,fe^32,ff^33,10c^20,127^20,130^131^132^133,138^12d^12f,141^20,146^20,15a^15b,160^34^35,161^35,162^36^37,163^35,164^38^39,16c^16d,273^fc^3a^152^16f^3b^170^171^153^15d^13f^12e^11e,274^10a^2a0^3c^154^14d^3d^14c^14b^14a^14f^14e^ec^111,275^21^22^2a1^10d^2a2^f5^139^2a3^130^2a4^13b^2a5^138^2a6^12b^2a7^13a,276^3e^167^2a8^152^3b,277^23^24^19b^25^22^114^c^278^26^f0^ef^f1,278^27^28^105^24^21d^21b^25^21c^21e^30^125^115^127^21f^288,279^ed^22^141^114^27a^ee,27a^27^104,27b^23^24^22^114^25^27a,27c^162^3f^40^41^163^42^43^161^160^3b,27d^1bd^44^22^114^3b^27e^fa,27e^107,27f^162^3f^18b^40^45^171^41^160^3b^1c2^161^1ca,280^46^47^22^2a9^110^48^1cb^c^278^2aa^43^f0^2ab^49^1a4^ef^2ac^1d0,281^1d1^10a^1d8^13b^12f^12b,282^4a^4b^21^10c^48^4c^1e2^104^42^1e4^43^1e5^49^1e3^283,283^27^2af^104^240^232^233^f6^2b0^30^234,285^3a^1ed^169^168^16b^eb^104^16a^1ee^3b^4d^26^44^2e^15c^11b^13b^15a^12f^12b^142^13a^121^115^146,287^4e^106^10b^105^2f^208^15f^11a^20f^2ad^f7,288^27^2ae^105^24^237^235^25^236^238^22^125,289^24,28a^fe^4f^229^227^50,28c^4f^23f^23d^50^23e,28e^3b^19b^4d^3a^100^44^f9,28f^27,290^3b^1bd^4d,291^22,292^149^116^12a,29d^fd^29e,29f^fd^29e,2a0^255^294^257^19^1c^18^1d^259^285^25b^1a^1b^17^1e^25d^25f^295^263^296^267^281^297^298^280^282^299^29a^29b^254,2e4^2f1^93^94^48^95^96^4e^2d^2f2^6e^42^43^92,2e5^2f3^2f2^6e^42^43,2e6^2e7^166,2e8^f7^ad,2e9^164^50^4f,2ea^22^32,2eb^2ea,2ec^b1,2ed^2ee^2ef,2ef^2f0^ed;;;"/>
    </map>

</incidents>
