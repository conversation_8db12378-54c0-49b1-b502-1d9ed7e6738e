package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.api.ApiResponse
import com.example.aimusicplayer.error.ErrorInfo
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.utils.NetworkResult
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import retrofit2.HttpException as RetrofitHttpException
import retrofit2.Response as RetrofitResponse
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject

/**
 * Flow ViewModel基类
 * 提供Flow相关的扩展函数和工具方法
 * 所有ViewModel应继承此基类，以确保架构一致性
 *
 * 继承自AndroidViewModel，提供Application上下文
 */
abstract class FlowViewModel(application: Application) : AndroidViewModel(application) {

    // 全局错误处理器
    // 由子类通过构造函数注入
    protected var errorHandler: GlobalErrorHandler? = null

    // 错误消息Flow
    protected val _errorMessageFlow = MutableSharedFlow<String>(
        replay = 0,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    val errorMessageFlow: SharedFlow<String> = _errorMessageFlow.asSharedFlow()
    open val errorMessage: LiveData<String> = errorMessageFlow.asLiveData()

    // 错误信息Flow
    protected val _errorInfoFlow = MutableSharedFlow<ErrorInfo>(
        replay = 0,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    val errorInfoFlow: SharedFlow<ErrorInfo> = _errorInfoFlow.asSharedFlow()

    // 加载状态Flow
    protected val _loadingFlow = MutableStateFlow(false)
    val loadingFlow: StateFlow<Boolean> = _loadingFlow.asStateFlow()
    open val loading: LiveData<Boolean> = loadingFlow.asLiveData()

    /**
     * 将MutableStateFlow转换为StateFlow
     */
    protected fun <T> MutableStateFlow<T>.toStateFlow(): StateFlow<T> = asStateFlow()

    /**
     * 将MutableSharedFlow转换为SharedFlow
     */
    protected fun <T> MutableSharedFlow<T>.toSharedFlow(): SharedFlow<T> = asSharedFlow()

    /**
     * 将Flow转换为StateFlow
     * @param initialValue 初始值
     * @param started 共享策略
     */
    protected fun <T> Flow<T>.toStateFlow(
        initialValue: T,
        started: SharingStarted = SharingStarted.WhileSubscribed(5000)
    ): StateFlow<T> = stateIn(
        scope = viewModelScope,
        started = started,
        initialValue = initialValue
    )

    /**
     * 将Flow转换为SharedFlow
     * @param started 共享策略
     */
    protected fun <T> Flow<T>.toSharedFlow(
        started: SharingStarted = SharingStarted.WhileSubscribed(5000)
    ): SharedFlow<T> = shareIn(
        scope = viewModelScope,
        started = started
    )

    /**
     * 将Flow转换为LiveData
     * 用于兼容旧代码，新代码应直接使用Flow
     */
    protected fun <T> Flow<T>.toLiveData(): LiveData<T> = asLiveData(viewModelScope.coroutineContext)

    /**
     * 发射事件
     * @param flow MutableSharedFlow
     * @param value 事件值
     */
    protected fun <T> emit(flow: MutableSharedFlow<T>, value: T) {
        viewModelScope.launch {
            flow.emit(value)
        }
    }

    /**
     * 更新状态
     * @param flow MutableStateFlow
     * @param value 新状态值
     */
    protected fun <T> update(flow: MutableStateFlow<T>, value: T) {
        flow.value = value
    }

    /**
     * 创建MutableStateFlow并初始化
     * @param initialValue 初始值
     */
    protected fun <T> mutableStateFlow(initialValue: T): MutableStateFlow<T> =
        MutableStateFlow(initialValue)

    /**
     * 创建MutableSharedFlow
     * @param replay 重放数量
     * @param extraBufferCapacity 额外缓冲容量
     * @param onBufferOverflow 缓冲区溢出策略
     */
    protected fun <T> mutableSharedFlow(
        replay: Int = 0,
        extraBufferCapacity: Int = 0,
        onBufferOverflow: BufferOverflow = BufferOverflow.SUSPEND
    ): MutableSharedFlow<T> = MutableSharedFlow(
        replay = replay,
        extraBufferCapacity = extraBufferCapacity,
        onBufferOverflow = onBufferOverflow
    )

    /**
     * 在协程中执行任务
     * @param block 要执行的代码块
     */
    protected fun launch(block: suspend CoroutineScope.() -> Unit): Job {
        return viewModelScope.launch {
            block()
        }
    }

    /**
     * 在IO线程中执行任务
     * @param block 要执行的代码块
     */
    protected fun launchIO(block: suspend CoroutineScope.() -> Unit): Job {
        return viewModelScope.launch(Dispatchers.IO) {
            block()
        }
    }

    /**
     * 处理异常的协程执行
     * @param onError 错误处理函数
     * @param block 要执行的代码块
     */
    protected fun launchSafely(
        onError: (Throwable) -> Unit = { handleError(it) },
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return viewModelScope.launch {
            try {
                block()
            } catch (e: Exception) {
                if (e is CancellationException) throw e
                onError(e)
            }
        }
    }

    /**
     * 处理错误
     * @param throwable 错误
     * @param customMessage 自定义错误消息
     * @param showToast 是否显示Toast
     */
    protected fun handleError(throwable: Throwable, customMessage: String? = null, showToast: Boolean = true) {
        Log.e(TAG, "Error in ViewModel", throwable)

        viewModelScope.launch {
            if (errorHandler != null) {
                // 使用全局错误处理器
                val errorInfo = errorHandler!!.handleError(throwable, showToast)
                _errorInfoFlow.emit(errorInfo)
                _errorMessageFlow.emit(errorInfo.message)
            } else {
                // 兼容旧版本
                val errorMsg = customMessage ?: ApiResponse.getNetworkErrorMessage(throwable)
                _errorMessageFlow.emit(errorMsg)
            }
        }
    }

    /**
     * 公共错误处理方法，供扩展函数调用
     */
    fun publicHandleError(throwable: Throwable, customMessage: String? = null, showToast: Boolean = true) {
        handleError(throwable, customMessage, showToast)
    }

    /**
     * 设置加载状态
     * @param isLoading 是否加载中
     */
    protected fun setLoading(isLoading: Boolean) {
        _loadingFlow.value = isLoading
    }

    /**
     * 公共设置加载状态方法，供扩展函数调用
     */
    fun publicSetLoading(isLoading: Boolean) {
        setLoading(isLoading)
    }

    /**
     * 创建网络请求Flow
     * @param call 挂起函数，执行网络请求
     * @param retryCount 重试次数
     * @param retryDelay 重试延迟（毫秒）
     * @return Flow<NetworkResult<T>>
     */
    protected fun <T> apiFlow(
        call: suspend () -> T,
        retryCount: Int = 0,
        retryDelay: Long = 1000
    ): Flow<NetworkResult<T>> = flow {
        emit(NetworkResult.Loading)

        var currentRetry = 0
        var lastError: Throwable? = null

        while (true) {
            try {
                val result = call()
                emit(NetworkResult.Success(result))
                break
            } catch (e: Exception) {
                if (e is CancellationException) throw e

                lastError = e

                if (currentRetry < retryCount) {
                    // 重试
                    currentRetry++
                    kotlinx.coroutines.delay(retryDelay)
                } else {
                    // 重试次数用完，抛出异常
                    throw e
                }
            }
        }
    }.onStart {
        setLoading(true)
    }.catch { e ->
        if (e is CancellationException) throw e

        // 处理错误
        handleError(e)

        // 获取错误消息
        val errorMessage = if (errorHandler != null) {
            // 使用全局错误处理器获取错误消息
            errorHandler!!.handleError(e, false).message
        } else {
            // 兼容旧版本
            ApiResponse.getNetworkErrorMessage(e)
        }

        emit(NetworkResult.Error(errorMessage))
    }.flowOn(Dispatchers.IO)

    /**
     * 创建Retrofit响应Flow
     * @param call 挂起函数，执行Retrofit请求
     * @param retryCount 重试次数
     * @param retryDelay 重试延迟（毫秒）
     * @return Flow<NetworkResult<T>>
     */
    protected fun <T> apiResponseFlow(
        call: suspend () -> RetrofitResponse<T>,
        retryCount: Int = 0,
        retryDelay: Long = 1000
    ): Flow<NetworkResult<T>> = flow {
        emit(NetworkResult.Loading)

        var currentRetry = 0
        var lastError: Throwable? = null

        while (true) {
            try {
                val response = call()
                if (response.isSuccessful) {
                    val body = response.body()
                    if (body != null) {
                        emit(NetworkResult.Success(body))
                    } else {
                        throw IllegalStateException("响应成功但数据为空")
                    }
                } else {
                    // 根据HTTP状态码决定是否重试
                    val shouldRetry = response.code() >= 500 || response.code() == 429

                    if (shouldRetry && currentRetry < retryCount) {
                        // 服务器错误或请求过于频繁，进行重试
                        currentRetry++
                        kotlinx.coroutines.delay(retryDelay)
                    } else {
                        // 客户端错误或重试次数用完，抛出异常
                        throw RetrofitHttpException(response)
                    }
                }

                // 如果成功处理了响应，跳出循环
                if (response.isSuccessful) {
                    break
                }
            } catch (e: Exception) {
                if (e is CancellationException) throw e

                lastError = e

                // 对于网络异常进行重试
                if (e is IOException && currentRetry < retryCount) {
                    currentRetry++
                    kotlinx.coroutines.delay(retryDelay)
                } else {
                    // 重试次数用完或非网络异常，抛出异常
                    throw e
                }
            }
        }
    }.onStart {
        setLoading(true)
    }.catch { e ->
        if (e is CancellationException) throw e

        // 处理错误
        handleError(e)

        // 获取错误消息
        val errorMessage = if (errorHandler != null) {
            // 使用全局错误处理器获取错误消息
            errorHandler!!.handleError(e, false).message
        } else {
            // 兼容旧版本
            ApiResponse.getNetworkErrorMessage(e)
        }

        emit(NetworkResult.Error(errorMessage))
    }.flowOn(Dispatchers.IO)

    /**
     * 获取应用上下文
     * 提供一个便捷方法获取应用上下文
     */
    protected fun getApplicationContext() = getApplication<Application>()

    /**
     * 创建UI状态Flow
     * @param initialState 初始状态
     * @return MutableStateFlow<T>
     */
    protected fun <T> createStateFlow(initialState: T): MutableStateFlow<T> {
        return MutableStateFlow(initialState)
    }

    /**
     * 创建UI事件Flow
     * @param replay 重放数量
     * @param extraBufferCapacity 额外缓冲容量
     * @param onBufferOverflow 缓冲区溢出策略
     * @return MutableSharedFlow<T>
     */
    protected fun <T> createEventFlow(
        replay: Int = 0,
        extraBufferCapacity: Int = 1,
        onBufferOverflow: BufferOverflow = BufferOverflow.DROP_OLDEST
    ): MutableSharedFlow<T> {
        return MutableSharedFlow(
            replay = replay,
            extraBufferCapacity = extraBufferCapacity,
            onBufferOverflow = onBufferOverflow
        )
    }

    /**
     * 发送UI事件
     * @param eventFlow 事件Flow
     * @param event 事件
     */
    protected fun <T> sendEvent(eventFlow: MutableSharedFlow<T>, event: T) {
        viewModelScope.launch {
            eventFlow.emit(event)
        }
    }

    /**
     * 清理资源
     * 子类可以重写此方法以清理资源
     */
    override fun onCleared() {
        super.onCleared()
        // 清理资源
        Log.d(TAG, "ViewModel cleared: ${this.javaClass.simpleName}")
    }

    companion object {
        private const val TAG = "FlowViewModel"
    }
}
