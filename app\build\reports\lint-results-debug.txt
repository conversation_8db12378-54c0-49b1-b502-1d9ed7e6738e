C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ButtonAnimationUtils.kt:163: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
            vibrator?.vibrate(VibrationEffect.createOneShot(duration, amplitude))
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ButtonAnimationUtils.kt:167: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
            vibrator?.vibrate(duration)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\CommentAdapter.kt:147: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\CommentAdapter.kt:170: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\CommentAdapter.kt:198: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\comment\CommentFragment.kt:214: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
                vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\player\PlayerFragment.kt:285: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
            vibrator.vibrate(android.os.VibrationEffect.createOneShot(20, android.os.VibrationEffect.DEFAULT_AMPLITUDE))
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\ReplyAdapter.kt:103: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\ReplyAdapter.kt:151: Error: Missing permissions required by Vibrator.vibrate: android.permission.VIBRATE [MissingPermission]
                    vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingPermission":
   This check scans through your code and libraries and looks at the APIs
   being used, and checks this against the set of permissions required to
   access those APIs. If the code using those APIs is called at runtime, then
   the program will crash.

   Furthermore, for permissions that are revocable (with targetSdkVersion 23),
   client code must also be prepared to handle the calls throwing an exception
   if the user rejects the request for permission at runtime.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\UnifiedPlaybackService.kt:225: Error: Overriding method should call super.onStartCommand [MissingSuperCall]
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
                 ~~~~~~~~~~~~~~

   Explanation for issues of type "MissingSuperCall":
   Some methods, such as View#onDetachedFromWindow, require that you also call
   the super implementation as part of your method.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\CommentAdapter.kt:62: Error: Do not treat position as fixed; only use immediately and call holder.getAdapterPosition() to look it up later [RecyclerView]
    override fun onBindViewHolder(holder: CommentViewHolder, position: Int) {
                                                             ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\adapter\PlaylistAdapter.java:51: Error: Do not treat position as fixed; only use immediately and call holder.getAdapterPosition() to look it up later [RecyclerView]
    public void onBindViewHolder(@NonNull PlaylistViewHolder holder, int position) {
                                                                     ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\ReplyAdapter.kt:48: Error: Do not treat position as fixed; only use immediately and call holder.getAdapterPosition() to look it up later [RecyclerView]
    override fun onBindViewHolder(holder: ReplyViewHolder, position: Int) {
                                                           ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\SongAdapter.kt:37: Error: Do not treat position as fixed; only use immediately and call holder.getAdapterPosition() to look it up later [RecyclerView]
    override fun onBindViewHolder(holder: SongViewHolder, position: Int) {
                                                          ~~~~~~~~~~~~~

   Explanation for issues of type "RecyclerView":
   RecyclerView will not call onBindViewHolder again when the position of the
   item changes in the data set unless the item itself is invalidated or the
   new position cannot be determined.

   For this reason, you should only use the position parameter while acquiring
   the related data item inside this method, and should not keep a copy of
   it.

   If you need the position of an item later on (e.g. in a click listener),
   use getAdapterPosition() which will have the updated adapter position.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12: Warning: WRITE_EXTERNAL_STORAGE no longer provides write access when targeting Android 11+, even when using requestLegacyExternalStorage [ScopedStorage]
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:438: Warning: The result of placeholder is not used [CheckResult]
            options.placeholder(placeholder)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:443: Warning: The result of error is not used [CheckResult]
            options.error(error)
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:448: Warning: The result of transform is not used [CheckResult]
            options.transform(CircleCrop())
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:450: Warning: The result of transform is not used [CheckResult]
            options.transform(RoundedCorners(cornerRadius))
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:455: Warning: The result of signature is not used [CheckResult]
            options.signature(ObjectKey(signature))
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:506: Warning: The result of apply is not used [CheckResult]
        request.apply(options)
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:511: Warning: The result of transition is not used [CheckResult]
                (request as RequestBuilder<Bitmap>).transition(BitmapTransitionOptions.withCrossFade())
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:513: Warning: The result of transition is not used [CheckResult]
                (request as RequestBuilder<Drawable>).transition(DrawableTransitionOptions.withCrossFade())
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:521: Warning: The result of listener is not used [CheckResult]
                (request as RequestBuilder<Bitmap>).listener(object : RequestListener<Bitmap> {
                ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:544: Warning: The result of listener is not used [CheckResult]
                (request as RequestBuilder<Drawable>).listener(object : RequestListener<Drawable> {
                ^

   Explanation for issues of type "CheckResult":
   Some methods have no side effects, and calling them without doing something
   with the result is suspicious.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\CacheStats.kt:57: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                ", hitRate=" + String.format("%.2f", getHitRate() * 100) + "%" +
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\EnhancedLyricParser.kt:268: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("[%02d:%02d.%02d]", minutes, seconds, milliseconds)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:58: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    switch (key.toLowerCase()) {
                                ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:254: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("[%02d:%02d.%03d]", minutes, seconds, milliseconds);
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\SongAdapter.kt:94: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%02d:%02d", minutes, seconds)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\TimeUtils.kt:16: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%02d:%02d", minutes, seconds)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\TimeUtils.kt:30: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\TimeUtils.kt:32: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String.format("%02d:%02d", minutes, seconds)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ButtonAnimationUtils.kt:159: Warning: Field requires API level 26 (current min is 24): android.os.VibrationEffect#DEFAULT_AMPLITUDE [InlinedApi]
        amplitude: Int = VibrationEffect.DEFAULT_AMPLITUDE
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:81: Warning: Field requires API level 33 (current min is 24): android.Manifest.permission#POST_NOTIFICATIONS [InlinedApi]
        Manifest.permission.POST_NOTIFICATIONS
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InlinedApi":
   This check scans through all the Android API field references in the
   application and flags certain constants, such as static final integers and
   Strings, which were introduced in later versions. These will actually be
   copied into the class files rather than being referenced, which means that
   the value is available even when running on older devices. In some cases
   that's fine, and in other cases it can result in a runtime crash or
   incorrect behavior. It depends on the context, so consider the code
   carefully and decide whether it's safe and can be suppressed or whether the
   code needs to be guarded.

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\UnifiedPlaybackService.kt:70: Error: Call requires API level 31 (current min is 24): java.lang.Class#getPackageName [NewApi]
        val EXTRA_NOTIFICATION = "${UnifiedPlaybackService::class.java.packageName}.notification"
                                                                       ~~~~~~~~~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_playlist.xml:27: Error: @id/text_playlist_title is not a sibling in the same RelativeLayout [NotSibling]
            android:layout_toEndOf="@id/text_playlist_title"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment.xml:193: Error: @+id/layout_like is not a sibling in the same ConstraintLayout [NotSibling]
        app:layout_constraintTop_toBottomOf="@+id/layout_like"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotSibling":
   Layout constraints in a given ConstraintLayout or RelativeLayout should
   reference other views within the same relative layout (but not itself!)

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:16: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application or sdk runs on a version of Android that is more
   recent than your targetSdkVersion specifies that it has been tested with,
   various compatibility modes kick in. This ensures that your application
   continues to work, but it may look out of place. For example, if the
   targetSdkVersion is less than 14, your app may get an option button in the
   UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\splash\SplashActivity.java:32: Warning: The application should not provide its own launch screen [CustomSplashScreen]
public class SplashActivity extends AppCompatActivity {
             ~~~~~~~~~~~~~~

   Explanation for issues of type "CustomSplashScreen":
   Starting in Android 12 (API 31+), the application's Launch Screen is
   provided by the system and the application should not create its own,
   otherwise the user will see two splashscreens. Please check the
   SplashScreen class to check how the Splash Screen can be controlled and
   customized.

   https://developer.android.com/guide/topics/ui/splash-screen

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\UnifiedPlaybackService.kt:227: Error: To call Service.startForeground(), the <service> element of manifest file must have the foregroundServiceType attribute specified [ForegroundServiceType]
        startForeground(Constants.NOTIFICATION_ID, createNotification())
        ~~~~~~~~~~~~~~~

   Explanation for issues of type "ForegroundServiceType":
   For targetSdkVersion >= 34, to call Service.startForeground(), the
   <service> element in the manifest file must have the foregroundServiceType
   attribute specified.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\player\PlayerFragment.kt:973: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
        val dialogView = layoutInflater.inflate(R.layout.dialog_playlist, null)
                                                                          ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19: Warning: Your app is currently not handling Selected Photos Access introduced in Android 14+ [SelectedPhotoAccess]
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20: Warning: Your app is currently not handling Selected Photos Access introduced in Android 14+ [SelectedPhotoAccess]
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SelectedPhotoAccess":
   Selected Photo Access is a new ability for users to share partial access to
   their photo library when apps request access to their device storage on
   Android 14+.

   Instead of letting the system manage the selection lifecycle, we recommend
   you adapt your app to handle partial access to the photo library.

   https://developer.android.com/about/versions/14/changes/partial-photo-video-access

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\UnifiedPlaybackService.kt:222: Error: playbackControlReceiver is missing RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED flag for unprotected broadcasts registered for com.example.aimusicplayer.ACTION_PLAY, com.example.aimusicplayer.ACTION_PAUSE, com.example.aimusicplayer.ACTION_PREVIOUS, com.example.aimusicplayer.ACTION_NEXT, com.example.aimusicplayer.ACTION_STOP [UnspecifiedRegisterReceiverFlag]
        registerReceiver(playbackControlReceiver, filter)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnspecifiedRegisterReceiverFlag":
   In Android U, all receivers registering for non-system broadcasts are
   required to include a flag indicating the receiver's exported state. Apps
   registering for non-system broadcasts should use the
   ContextCompat#registerReceiver APIs with flags set to either
   RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED.

   If you are not expecting broadcasts from other apps on the device, register
   your receiver with RECEIVER_NOT_EXPORTED to protect your receiver on all
   platform releases.

   https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_playing_disc.xml:3: Warning: Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more [VectorRaster]
    android:width="300dp"
                   ~~~~~

   Explanation for issues of type "VectorRaster":
   Vector icons require API 21 or API 24 depending on used features, but when
   minSdkVersion is less than 21 or 24 and Android Gradle plugin 1.4 or higher
   is used, a vector drawable placed in the drawable folder is automatically
   moved to drawable-anydpi-v21 or drawable-anydpi-v24 and bitmap images are
   generated for different screen resolutions for backwards compatibility.

   However, there are some limitations to this raster image generation, and
   this lint check flags elements and attributes that are not fully supported.
   You should manually check whether the generated output is acceptable for
   those older devices.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:73: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0 [GradleDependency]
    implementation 'androidx.appcompat:appcompat:1.6.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:74: Warning: A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0 [GradleDependency]
    implementation 'com.google.android.material:material:1.11.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:75: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:78: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0 [GradleDependency]
    implementation 'com.google.code.gson:gson:2.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:90: Warning: A newer version of androidx.media3:media3-exoplayer than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation 'androidx.media3:media3-exoplayer:1.2.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:91: Warning: A newer version of androidx.media3:media3-ui than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation 'androidx.media3:media3-ui:1.2.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:92: Warning: A newer version of androidx.media3:media3-session than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation 'androidx.media3:media3-session:1.2.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:93: Warning: A newer version of androidx.media3:media3-common than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation 'androidx.media3:media3-common:1.2.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:94: Warning: A newer version of androidx.media3:media3-datasource-okhttp than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation 'androidx.media3:media3-datasource-okhttp:1.2.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:113: Warning: A newer version of com.google.zxing:core than 3.4.1 is available: 3.5.1 [GradleDependency]
    implementation 'com.google.zxing:core:3.4.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:114: Warning: A newer version of com.journeyapps:zxing-android-embedded than 4.2.0 is available: 4.3.0 [GradleDependency]
    implementation 'com.journeyapps:zxing-android-embedded:4.2.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:122: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:123: Warning: A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:124: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:125: Warning: A newer version of androidx.lifecycle:lifecycle-common-java8 than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:132: Warning: A newer version of com.airbnb.android:lottie than 6.1.0 is available: 6.3.0 [GradleDependency]
    implementation 'com.airbnb.android:lottie:6.1.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:138: Warning: A newer version of androidx.navigation:navigation-fragment-ktx than 2.7.5 is available: 2.9.0 [GradleDependency]
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.5'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:139: Warning: A newer version of androidx.navigation:navigation-ui-ktx than 2.7.5 is available: 2.9.0 [GradleDependency]
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.5'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:142: Warning: A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1 [GradleDependency]
    implementation 'androidx.room:room-runtime:2.6.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:143: Warning: A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1 [GradleDependency]
    implementation 'androidx.room:room-ktx:2.6.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:145: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1 [GradleDependency]
    ksp 'androidx.room:room-compiler:2.6.1'
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:148: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:149: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\data\source\MusicDataSource.kt:700: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
        override fun createDataSource(): DataSource {
                     ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\data\source\MusicDataSource.kt:701: Error: This declaration is opt-in and its usage should be marked with @androidx.media3.common.util.UnstableApi or @OptIn(markerClass = androidx.media3.common.util.UnstableApi.class) [UnsafeOptInUsageError from androidx.annotation.experimental]
            return defaultDataSourceFactory.createDataSource()
                                            ~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnsafeOptInUsageError":
   This API has been flagged as opt-in with error-level severity.

   Any declaration annotated with this marker is considered part of an
   unstable or
   otherwise non-standard API surface and its call sites should accept the
   opt-in
   aspect of it by using the @OptIn annotation, using the marker annotation
   --
   effectively causing further propagation of the opt-in aspect -- or
   configuring
   the UnsafeOptInUsageError check's options for project-wide opt-in.

   To configure project-wide opt-in, specify the opt-in option value in
   lint.xml
   as a comma-delimited list of opted-in annotations:

   <lint>
       <issue id="UnsafeOptInUsageError">
           <option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
       </issue>
   </lint>

   Vendor: Android Open Source Project
   Identifier: androidx.annotation.experimental
   Feedback: https://issuetracker.google.com/issues/new?component=459778

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:63: Warning: Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed. [DiscouragedApi]
            android:screenOrientation="landscape"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:75: Warning: Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed. [DiscouragedApi]
            android:screenOrientation="landscape"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:82: Warning: Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed. [DiscouragedApi]
            android:screenOrientation="landscape"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ImageUtils.kt:469: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
                    val resourceId = context.resources.getIdentifier(
                                                       ~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:64: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@color/sakura_text_secondary"/>
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:154: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                    android:tint="#FFFFFF"
                    ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:165: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                    android:tint="#3498db"
                    ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:176: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                    android:tint="#FFFFFF" />
                    ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:203: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="#3498db"
                        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:231: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="#E74C3C"
                        ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseAppTint":
   ImageView or ImageButton uses android:tint instead of app:tint

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:95: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
                            android:drawableStart="@drawable/ic_favorite"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:97: Warning: Use app:drawableTint instead of android:drawableTint [UseCompatTextViewDrawableXml from androidx.appcompat]
                            android:drawableTint="#F1C40F"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseCompatTextViewDrawableXml":
   TextView uses android: compound drawable attributes instead of app: ones

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\xml\network_security_config.xml:6: Warning: The Network Security Configuration allows the use of user certificates in the release version of your app [AcceptsUserCertificates]
            <certificates src="user" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AcceptsUserCertificates":
   Allowing user certificates could allow eavesdroppers to intercept data sent
   by your app, which could impact the privacy of your users. Consider nesting
   your app's trust-anchors inside a <debug-overrides> element to make sure
   they are only available when android:debuggable is set to true.

   https://goo.gle/AcceptsUserCertificates
   https://developer.android.com/training/articles/security-config#TrustingDebugCa

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\xml\network_security_config.xml:3: Warning: Insecure Base Configuration [InsecureBaseConfiguration]
    <base-config cleartextTrafficPermitted="true">
                                            ~~~~

   Explanation for issues of type "InsecureBaseConfiguration":
   Permitting cleartext traffic could allow eavesdroppers to intercept data
   sent by your app, which impacts the privacy of your users. Consider only
   allowing encrypted traffic by setting the cleartextTrafficPermitted tag to
   false.

   https://goo.gle/InsecureBaseConfiguration
   https://developer.android.com/preview/features/security-config.html

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\adapter\TopListAdapter.java:66: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\discovery\TopListFragment.java:108: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                        adapter.notifyDataSetChanged();
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\AlbumRotationController.kt:445: Warning: This animation should be started with #start() [Recycle]
        val animatorSet = AnimatorSet()
                          ~~~~~~~~~~~

   Explanation for issues of type "Recycle":
   Many resources, such as TypedArrays, VelocityTrackers, etc., should be
   recycled (with a recycle() call) after use. This lint check looks for
   missing recycle() calls.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\AlbumRotationController.kt:104: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\AlbumRotationController.kt:117: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\error\GlobalErrorHandler.kt:164: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:423: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\NetworkUtils.kt:28: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\NetworkUtils.kt:53: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\NetworkUtils.kt:78: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable-v24: Warning: This folder configuration (v24) is unnecessary; minSdkVersion is 24. Merge all the resources in this folder into drawable. [ObsoleteSdkInt]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values-night\themes.xml:13: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        <item name="android:statusBarColor" tools:targetApi="21">?attr/colorPrimaryVariant</item>
                                            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\themes.xml:13: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        <item name="android:statusBarColor" tools:targetApi="21">?attr/colorPrimaryVariant</item>
                                            ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\AlbumArtCache.kt:33: Warning: Do not place Android context classes in static fields (static reference to AlbumArtCache which has field context pointing to Context); this is a memory leak [StaticFieldLeak]
        // 单例实例
        ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\CacheManager.kt:376: Warning: Do not place Android context classes in static fields (static reference to CacheManager which has field context pointing to Context); this is a memory leak [StaticFieldLeak]
        // 单例实例
        ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\EnhancedImageCache.kt:34: Warning: Do not place Android context classes in static fields (static reference to EnhancedImageCache which has field context pointing to Context); this is a memory leak [StaticFieldLeak]
        // 单例实例
        ^

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment.xml:97: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
            <LinearLayout
             ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment.xml:135: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
            <LinearLayout
             ~~~~~~~~~~~~

   Explanation for issues of type "UseCompoundDrawables":
   A LinearLayout which contains an ImageView and a TextView can be more
   efficiently handled as a compound drawable (a single TextView, using the
   drawableTop, drawableLeft, drawableRight and/or drawableBottom attributes
   to draw one or more images adjacent to the text).

   If the two widgets are offset from each other with margins, this can be
   replaced with a drawablePadding attribute.

   There's a lint quickfix to perform this conversion in the Eclipse plugin.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_settings.xml:9: Warning: Very long vector path (906 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94 0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87c-0.12,0.21 -0.08,0.47 0.12,0.61l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6 3.6,1.62 3.6,3.6 -1.62,3.6 -3.6,3.6z"/>
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\icon_settings.xml:9: Warning: Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z"/>
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:36: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:26: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_splash.xml:7: Warning: Possible overdraw: Root element paints background @color/splash_background with a theme that also paints a background (inferred theme is @style/FullScreenTheme) [Overdraw]
    android:background="@color/splash_background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:8: Warning: Possible overdraw: Root element paints background ?colorBackground with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="?colorBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_intelligence.xml:8: Warning: Possible overdraw: Root element paints background ?colorBackground with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="?colorBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_comment.xml:7: Warning: Possible overdraw: Root element paints background @color/background_dark with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="@color/background_dark">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:6: Warning: Possible overdraw: Root element paints background #000022 with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="#000022"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_intelligence.xml:7: Warning: Possible overdraw: Root element paints background @color/background_color with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="@color/background_color">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:7: Warning: Possible overdraw: Root element paints background @color/black with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="@color/black">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_hot_search.xml:10: Warning: Possible overdraw: Root element paints background ?android:attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="?android:attr/selectableItemBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_reply.xml:10: Warning: Possible overdraw: Root element paints background ?attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="?attr/selectableItemBackground">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_search_suggest.xml:10: Warning: Possible overdraw: Root element paints background ?android:attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="?android:attr/selectableItemBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_song.xml:7: Warning: Possible overdraw: Root element paints background ?attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="?attr/selectableItemBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml:5: Warning: Possible overdraw: Root element paints background #EFEFEF with a theme that also paints a background (inferred theme is @style/Theme.AIMusicPlayer) [Overdraw]
    android:background="#EFEFEF"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_player.xml:2: Warning: The resource R.layout.activity_player appears to be unused [UnusedResources]
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_button.xml:2: Warning: The resource R.drawable.bg_button appears to be unused [UnusedResources]
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_comment_success.xml:2: Warning: The resource R.drawable.bg_comment_success appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_edit_text.xml:2: Warning: The resource R.drawable.bg_edit_text appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_playing_cover_border.xml:2: Warning: The resource R.drawable.bg_playing_cover_border appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_playing_playback_progress.xml:2: Warning: The resource R.drawable.bg_playing_playback_progress appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\button_playlist_close.xml:2: Warning: The resource R.drawable.button_playlist_close appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\button_press_feedback.xml:2: Warning: The resource R.anim.button_press_feedback appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\button_release_feedback.xml:2: Warning: The resource R.anim.button_release_feedback appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\button_secondary.xml:2: Warning: The resource R.drawable.button_secondary appears to be unused [UnusedResources]
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-hdpi\cherry_blossom_car.webp: Warning: The resource R.mipmap.cherry_blossom_car appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\cherry_blossom_car_background.xml:2: Warning: The resource R.drawable.cherry_blossom_car_background appears to be unused [UnusedResources]
<vector
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-hdpi\cherry_blossom_car_foreground.webp: Warning: The resource R.mipmap.cherry_blossom_car_foreground appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-hdpi\cherry_blossom_car_round.webp: Warning: The resource R.mipmap.cherry_blossom_car_round appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.color_transparent appears to be unused [UnusedResources]
    <color name="color_transparent">#00000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.color_gray_50 appears to be unused [UnusedResources]
    <color name="color_gray_50">#FAFAFA</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.color_gray_100 appears to be unused [UnusedResources]
    <color name="color_gray_100">#F5F5F5</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.color_gray_200 appears to be unused [UnusedResources]
    <color name="color_gray_200">#EEEEEE</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:11: Warning: The resource R.color.color_gray_400 appears to be unused [UnusedResources]
    <color name="color_gray_400">#BDBDBD</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.color_gray_600 appears to be unused [UnusedResources]
    <color name="color_gray_600">#757575</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:14: Warning: The resource R.color.color_gray_700 appears to be unused [UnusedResources]
    <color name="color_gray_700">#616161</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:15: Warning: The resource R.color.color_gray_800 appears to be unused [UnusedResources]
    <color name="color_gray_800">#424242</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:16: Warning: The resource R.color.color_gray_900 appears to be unused [UnusedResources]
    <color name="color_gray_900">#212121</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:17: Warning: The resource R.color.color_blue_500 appears to be unused [UnusedResources]
    <color name="color_blue_500">#2196F3</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:18: Warning: The resource R.color.color_blue_700 appears to be unused [UnusedResources]
    <color name="color_blue_700">#1976D2</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:19: Warning: The resource R.color.color_blue_900 appears to be unused [UnusedResources]
    <color name="color_blue_900">#0D47A1</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:20: Warning: The resource R.color.color_light_blue_500 appears to be unused [UnusedResources]
    <color name="color_light_blue_500">#03A9F4</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:21: Warning: The resource R.color.color_light_blue_700 appears to be unused [UnusedResources]
    <color name="color_light_blue_700">#0288D1</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:22: Warning: The resource R.color.color_pink_500 appears to be unused [UnusedResources]
    <color name="color_pink_500">#E91E63</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.color_pink_700 appears to be unused [UnusedResources]
    <color name="color_pink_700">#C2185B</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:24: Warning: The resource R.color.color_red_500 appears to be unused [UnusedResources]
    <color name="color_red_500">#F44336</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:25: Warning: The resource R.color.color_green_500 appears to be unused [UnusedResources]
    <color name="color_green_500">#4CAF50</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:26: Warning: The resource R.color.color_yellow_500 appears to be unused [UnusedResources]
    <color name="color_yellow_500">#FFEB3B</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:36: Warning: The resource R.color.theme_primary_light appears to be unused [UnusedResources]
    <color name="theme_primary_light">#BBDEFB</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:38: Warning: The resource R.color.theme_accent_dark appears to be unused [UnusedResources]
    <color name="theme_accent_dark">#F50057</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:39: Warning: The resource R.color.theme_accent_light appears to be unused [UnusedResources]
    <color name="theme_accent_light">#FF80AB</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:41: Warning: The resource R.color.theme_surface appears to be unused [UnusedResources]
    <color name="theme_surface">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:42: Warning: The resource R.color.theme_error appears to be unused [UnusedResources]
    <color name="theme_error">#F44336</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:43: Warning: The resource R.color.theme_success appears to be unused [UnusedResources]
    <color name="theme_success">#4CAF50</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:44: Warning: The resource R.color.theme_warning appears to be unused [UnusedResources]
    <color name="theme_warning">#FFC107</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:45: Warning: The resource R.color.theme_info appears to be unused [UnusedResources]
    <color name="theme_info">#2196F3</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:51: Warning: The resource R.color.text_disabled appears to be unused [UnusedResources]
    <color name="text_disabled">#9E9E9E</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:53: Warning: The resource R.color.text_dark appears to be unused [UnusedResources]
    <color name="text_dark">#000000</color>
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:54: Warning: The resource R.color.text_link appears to be unused [UnusedResources]
    <color name="text_link">#2196F3</color>
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:57: Warning: The resource R.color.ui_divider appears to be unused [UnusedResources]
    <color name="ui_divider">#E0E0E0</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:58: Warning: The resource R.color.ui_ripple appears to be unused [UnusedResources]
    <color name="ui_ripple">#80FFFFFF</color>
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:60: Warning: The resource R.color.ui_button_text appears to be unused [UnusedResources]
    <color name="ui_button_text">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:64: Warning: The resource R.color.search_background appears to be unused [UnusedResources]
    <color name="search_background">#F8F9FA</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:65: Warning: The resource R.color.search_stroke appears to be unused [UnusedResources]
    <color name="search_stroke">#DADCE0</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:68: Warning: The resource R.color.nav_background appears to be unused [UnusedResources]
    <color name="nav_background">#000000</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:69: Warning: The resource R.color.nav_icon_active appears to be unused [UnusedResources]
    <color name="nav_icon_active">#1E88E5</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:70: Warning: The resource R.color.nav_icon_inactive appears to be unused [UnusedResources]
    <color name="nav_icon_inactive">#787878</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:75: Warning: The resource R.color.sidebar_item_text appears to be unused [UnusedResources]
    <color name="sidebar_item_text">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:76: Warning: The resource R.color.sidebar_item_icon_normal appears to be unused [UnusedResources]
    <color name="sidebar_item_icon_normal">#AAAAAA</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:77: Warning: The resource R.color.sidebar_item_icon_selected appears to be unused [UnusedResources]
    <color name="sidebar_item_icon_selected">#2196F3</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:78: Warning: The resource R.color.sidebar_item_background_selected appears to be unused [UnusedResources]
    <color name="sidebar_item_background_selected">#303030</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:81: Warning: The resource R.color.player_background appears to be unused [UnusedResources]
    <color name="player_background">#121212</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:82: Warning: The resource R.color.player_controls appears to be unused [UnusedResources]
    <color name="player_controls">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:83: Warning: The resource R.color.player_progress_background appears to be unused [UnusedResources]
    <color name="player_progress_background">#4D4D4D</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:84: Warning: The resource R.color.player_progress appears to be unused [UnusedResources]
    <color name="player_progress">#2196F3</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:85: Warning: The resource R.color.player_control_background appears to be unused [UnusedResources]
    <color name="player_control_background">#EDEDED</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:88: Warning: The resource R.color.lyric_background appears to be unused [UnusedResources]
    <color name="lyric_background">#212121</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:89: Warning: The resource R.color.lyric_highlight appears to be unused [UnusedResources]
    <color name="lyric_highlight">#2196F3</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:90: Warning: The resource R.color.lyric_normal appears to be unused [UnusedResources]
    <color name="lyric_normal">#9E9E9E</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:93: Warning: The resource R.color.list_item_background appears to be unused [UnusedResources]
    <color name="list_item_background">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:94: Warning: The resource R.color.list_item_background_selected appears to be unused [UnusedResources]
    <color name="list_item_background_selected">#E3F2FD</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:97: Warning: The resource R.color.driving_mode_background appears to be unused [UnusedResources]
    <color name="driving_mode_background">#000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:98: Warning: The resource R.color.driving_mode_text appears to be unused [UnusedResources]
    <color name="driving_mode_text">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:99: Warning: The resource R.color.driving_mode_controls appears to be unused [UnusedResources]
    <color name="driving_mode_controls">#2196F3</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:119: Warning: The resource R.color.transparent appears to be unused [UnusedResources]
    <color name="transparent">@color/color_transparent</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:120: Warning: The resource R.color.gray appears to be unused [UnusedResources]
    <color name="gray">@color/color_gray_500</color>
           ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:121: Warning: The resource R.color.light_gray appears to be unused [UnusedResources]
    <color name="light_gray">@color/color_gray_300</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:122: Warning: The resource R.color.dark_gray appears to be unused [UnusedResources]
    <color name="dark_gray">@color/color_gray_700</color>
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:123: Warning: The resource R.color.red appears to be unused [UnusedResources]
    <color name="red">@color/color_red_500</color>
           ~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:124: Warning: The resource R.color.green appears to be unused [UnusedResources]
    <color name="green">@color/color_green_500</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:125: Warning: The resource R.color.blue appears to be unused [UnusedResources]
    <color name="blue">@color/color_blue_500</color>
           ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:126: Warning: The resource R.color.yellow appears to be unused [UnusedResources]
    <color name="yellow">@color/color_yellow_500</color>
           ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:129: Warning: The resource R.color.primary appears to be unused [UnusedResources]
    <color name="primary">@color/theme_primary</color>
           ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:130: Warning: The resource R.color.primary_dark appears to be unused [UnusedResources]
    <color name="primary_dark">@color/theme_primary_dark</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:131: Warning: The resource R.color.primary_light appears to be unused [UnusedResources]
    <color name="primary_light">@color/theme_primary_light</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:132: Warning: The resource R.color.accent appears to be unused [UnusedResources]
    <color name="accent">@color/theme_accent</color>
           ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:133: Warning: The resource R.color.accent_dark appears to be unused [UnusedResources]
    <color name="accent_dark">@color/theme_accent_dark</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:134: Warning: The resource R.color.accent_light appears to be unused [UnusedResources]
    <color name="accent_light">@color/theme_accent_light</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:135: Warning: The resource R.color.background appears to be unused [UnusedResources]
    <color name="background">@color/theme_background</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:136: Warning: The resource R.color.divider appears to be unused [UnusedResources]
    <color name="divider">@color/ui_divider</color>
           ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:137: Warning: The resource R.color.success appears to be unused [UnusedResources]
    <color name="success">@color/theme_success</color>
           ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:138: Warning: The resource R.color.info appears to be unused [UnusedResources]
    <color name="info">@color/theme_info</color>
           ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:139: Warning: The resource R.color.warning appears to be unused [UnusedResources]
    <color name="warning">@color/theme_warning</color>
           ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:140: Warning: The resource R.color.error appears to be unused [UnusedResources]
    <color name="error">@color/theme_error</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:144: Warning: The resource R.color.primary_dark_color appears to be unused [UnusedResources]
    <color name="primary_dark_color">@color/theme_primary_dark</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:149: Warning: The resource R.color.divider_color appears to be unused [UnusedResources]
    <color name="divider_color">@color/ui_divider</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:151: Warning: The resource R.color.button_text_color appears to be unused [UnusedResources]
    <color name="button_text_color">@color/ui_button_text</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:152: Warning: The resource R.color.ripple_color appears to be unused [UnusedResources]
    <color name="ripple_color">@color/ui_ripple</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:154: Warning: The resource R.color.lyric_background_color appears to be unused [UnusedResources]
    <color name="lyric_background_color">@color/lyric_background</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:155: Warning: The resource R.color.lyric_highlight_color appears to be unused [UnusedResources]
    <color name="lyric_highlight_color">@color/lyric_highlight</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:156: Warning: The resource R.color.lyric_normal_color appears to be unused [UnusedResources]
    <color name="lyric_normal_color">@color/lyric_normal</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:159: Warning: The resource R.color.search_bg_color appears to be unused [UnusedResources]
    <color name="search_bg_color">@color/search_background</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:160: Warning: The resource R.color.search_stroke_color appears to be unused [UnusedResources]
    <color name="search_stroke_color">@color/search_stroke</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:168: Warning: The resource R.color.colorBackground appears to be unused [UnusedResources]
    <color name="colorBackground">@color/theme_background</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:169: Warning: The resource R.color.colorTextPrimary appears to be unused [UnusedResources]
    <color name="colorTextPrimary">@color/text_primary</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:170: Warning: The resource R.color.colorTextSecondary appears to be unused [UnusedResources]
    <color name="colorTextSecondary">@color/text_secondary</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml:173: Warning: The resource R.color.color_background appears to be unused [UnusedResources]
    <color name="color_background">@color/theme_background</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\comment_like_animation.xml:2: Warning: The resource R.anim.comment_like_animation appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\comment_send_success.xml:2: Warning: The resource R.anim.comment_send_success appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\control_button_background.xml:2: Warning: The resource R.drawable.control_button_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\custom_app_icon.xml:2: Warning: The resource R.drawable.custom_app_icon appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\dark_blue_gradient_background.xml:2: Warning: The resource R.drawable.dark_blue_gradient_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\dialog_background.xml:2: Warning: The resource R.drawable.dialog_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:2: Warning: The resource R.layout.dialog_comment appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_heart_mode.xml:2: Warning: The resource R.layout.dialog_heart_mode appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_intelligence.xml:2: Warning: The resource R.layout.dialog_intelligence appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:4: Warning: The resource R.dimen.text_size_headline appears to be unused [UnusedResources]
    <dimen name="text_size_headline">24sp</dimen>         <!-- 大标题 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:5: Warning: The resource R.dimen.text_size_title appears to be unused [UnusedResources]
    <dimen name="text_size_title">20sp</dimen>            <!-- 标题 -->
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:6: Warning: The resource R.dimen.text_size_subtitle appears to be unused [UnusedResources]
    <dimen name="text_size_subtitle">18sp</dimen>         <!-- 副标题 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:7: Warning: The resource R.dimen.text_size_body appears to be unused [UnusedResources]
    <dimen name="text_size_body">16sp</dimen>             <!-- 正文 -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:8: Warning: The resource R.dimen.text_size_caption appears to be unused [UnusedResources]
    <dimen name="text_size_caption">14sp</dimen>          <!-- 说明文字 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:9: Warning: The resource R.dimen.text_size_small appears to be unused [UnusedResources]
    <dimen name="text_size_small">12sp</dimen>            <!-- 小字体 -->
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:12: Warning: The resource R.dimen.driving_text_size_title appears to be unused [UnusedResources]
    <dimen name="driving_text_size_title">30sp</dimen>    <!-- 驾驶模式标题 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:13: Warning: The resource R.dimen.driving_text_size_body appears to be unused [UnusedResources]
    <dimen name="driving_text_size_body">24sp</dimen>     <!-- 驾驶模式正文 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:14: Warning: The resource R.dimen.driving_text_size_small appears to be unused [UnusedResources]
    <dimen name="driving_text_size_small">20sp</dimen>    <!-- 驾驶模式小字 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:17: Warning: The resource R.dimen.margin_tiny appears to be unused [UnusedResources]
    <dimen name="margin_tiny">2dp</dimen>                 <!-- 极小边距 -->
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:18: Warning: The resource R.dimen.margin_small appears to be unused [UnusedResources]
    <dimen name="margin_small">4dp</dimen>                <!-- 小边距 -->
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:19: Warning: The resource R.dimen.margin_medium appears to be unused [UnusedResources]
    <dimen name="margin_medium">8dp</dimen>               <!-- 中等边距 -->
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:20: Warning: The resource R.dimen.margin_normal appears to be unused [UnusedResources]
    <dimen name="margin_normal">16dp</dimen>              <!-- 标准边距 -->
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:21: Warning: The resource R.dimen.margin_large appears to be unused [UnusedResources]
    <dimen name="margin_large">24dp</dimen>               <!-- 大边距 -->
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:22: Warning: The resource R.dimen.margin_xlarge appears to be unused [UnusedResources]
    <dimen name="margin_xlarge">32dp</dimen>              <!-- 超大边距 -->
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:23: Warning: The resource R.dimen.margin_xxlarge appears to be unused [UnusedResources]
    <dimen name="margin_xxlarge">48dp</dimen>             <!-- 特大边距 -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:29: Warning: The resource R.dimen.padding_tiny appears to be unused [UnusedResources]
    <dimen name="padding_tiny">2dp</dimen>                <!-- 极小内边距 -->
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:30: Warning: The resource R.dimen.padding_small appears to be unused [UnusedResources]
    <dimen name="padding_small">4dp</dimen>               <!-- 小内边距 -->
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:31: Warning: The resource R.dimen.padding_medium appears to be unused [UnusedResources]
    <dimen name="padding_medium">8dp</dimen>              <!-- 中等内边距 -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:32: Warning: The resource R.dimen.padding_normal appears to be unused [UnusedResources]
    <dimen name="padding_normal">16dp</dimen>             <!-- 标准内边距 -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:33: Warning: The resource R.dimen.padding_large appears to be unused [UnusedResources]
    <dimen name="padding_large">24dp</dimen>              <!-- 大内边距 -->
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:34: Warning: The resource R.dimen.padding_xlarge appears to be unused [UnusedResources]
    <dimen name="padding_xlarge">32dp</dimen>             <!-- 超大内边距 -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:37: Warning: The resource R.dimen.corner_radius_small appears to be unused [UnusedResources]
    <dimen name="corner_radius_small">4dp</dimen>         <!-- 小圆角 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:38: Warning: The resource R.dimen.corner_radius_medium appears to be unused [UnusedResources]
    <dimen name="corner_radius_medium">8dp</dimen>        <!-- 中等圆角 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:39: Warning: The resource R.dimen.corner_radius_large appears to be unused [UnusedResources]
    <dimen name="corner_radius_large">16dp</dimen>        <!-- 大圆角 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:42: Warning: The resource R.dimen.height_button appears to be unused [UnusedResources]
    <dimen name="height_button">48dp</dimen>              <!-- 按钮高度 -->
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:43: Warning: The resource R.dimen.height_toolbar appears to be unused [UnusedResources]
    <dimen name="height_toolbar">56dp</dimen>             <!-- 工具栏高度 -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:44: Warning: The resource R.dimen.height_navigation_item appears to be unused [UnusedResources]
    <dimen name="height_navigation_item">56dp</dimen>     <!-- 导航项高度 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:45: Warning: The resource R.dimen.height_divider appears to be unused [UnusedResources]
    <dimen name="height_divider">1dp</dimen>              <!-- 分隔线高度 -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:46: Warning: The resource R.dimen.height_seekbar appears to be unused [UnusedResources]
    <dimen name="height_seekbar">24dp</dimen>             <!-- 进度条高度 -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:47: Warning: The resource R.dimen.height_list_item appears to be unused [UnusedResources]
    <dimen name="height_list_item">72dp</dimen>           <!-- 列表项高度 -->
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:48: Warning: The resource R.dimen.height_card appears to be unused [UnusedResources]
    <dimen name="height_card">180dp</dimen>               <!-- 卡片高度 -->
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:51: Warning: The resource R.dimen.width_sidebar appears to be unused [UnusedResources]
    <dimen name="width_sidebar">80dp</dimen>              <!-- 侧边栏宽度 -->
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:52: Warning: The resource R.dimen.width_icon appears to be unused [UnusedResources]
    <dimen name="width_icon">24dp</dimen>                 <!-- 图标宽度 -->
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:53: Warning: The resource R.dimen.width_fab appears to be unused [UnusedResources]
    <dimen name="width_fab">56dp</dimen>                  <!-- 浮动按钮宽度 -->
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:54: Warning: The resource R.dimen.width_sidebar_selection_indicator appears to be unused [UnusedResources]
    <dimen name="width_sidebar_selection_indicator">4dp</dimen> <!-- 侧边栏选中指示器宽度 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:57: Warning: The resource R.dimen.image_thumbnail_small appears to be unused [UnusedResources]
    <dimen name="image_thumbnail_small">40dp</dimen>      <!-- 小缩略图 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:58: Warning: The resource R.dimen.image_thumbnail_medium appears to be unused [UnusedResources]
    <dimen name="image_thumbnail_medium">60dp</dimen>     <!-- 中缩略图 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:59: Warning: The resource R.dimen.image_thumbnail_large appears to be unused [UnusedResources]
    <dimen name="image_thumbnail_large">80dp</dimen>      <!-- 大缩略图 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:60: Warning: The resource R.dimen.image_cover_small appears to be unused [UnusedResources]
    <dimen name="image_cover_small">120dp</dimen>         <!-- 小封面 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:61: Warning: The resource R.dimen.image_cover_medium appears to be unused [UnusedResources]
    <dimen name="image_cover_medium">180dp</dimen>        <!-- 中封面 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:62: Warning: The resource R.dimen.image_cover_large appears to be unused [UnusedResources]
    <dimen name="image_cover_large">240dp</dimen>         <!-- 大封面 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:63: Warning: The resource R.dimen.image_cover_xlarge appears to be unused [UnusedResources]
    <dimen name="image_cover_xlarge">320dp</dimen>        <!-- 特大封面 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:66: Warning: The resource R.dimen.player_control_size_small appears to be unused [UnusedResources]
    <dimen name="player_control_size_small">40dp</dimen>  <!-- 小控制按钮 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:67: Warning: The resource R.dimen.player_control_size_medium appears to be unused [UnusedResources]
    <dimen name="player_control_size_medium">56dp</dimen> <!-- 中控制按钮 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:68: Warning: The resource R.dimen.player_control_size_large appears to be unused [UnusedResources]
    <dimen name="player_control_size_large">72dp</dimen>  <!-- 大控制按钮 (播放/暂停) -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:69: Warning: The resource R.dimen.player_seekbar_height appears to be unused [UnusedResources]
    <dimen name="player_seekbar_height">6dp</dimen>       <!-- 进度条高度 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:70: Warning: The resource R.dimen.player_seekbar_thumb appears to be unused [UnusedResources]
    <dimen name="player_seekbar_thumb">16dp</dimen>       <!-- 进度条拖动点 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:73: Warning: The resource R.dimen.driving_control_size appears to be unused [UnusedResources]
    <dimen name="driving_control_size">96dp</dimen>       <!-- 驾驶模式控制按钮 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:74: Warning: The resource R.dimen.driving_seekbar_height appears to be unused [UnusedResources]
    <dimen name="driving_seekbar_height">12dp</dimen>     <!-- 驾驶模式进度条高度 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:75: Warning: The resource R.dimen.driving_seekbar_thumb appears to be unused [UnusedResources]
    <dimen name="driving_seekbar_thumb">24dp</dimen>      <!-- 驾驶模式进度条拖动点 -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:78: Warning: The resource R.integer.anim_duration_short appears to be unused [UnusedResources]
    <integer name="anim_duration_short">150</integer>     <!-- 短动画 -->
             ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:79: Warning: The resource R.integer.anim_duration_medium appears to be unused [UnusedResources]
    <integer name="anim_duration_medium">300</integer>    <!-- 中等动画 -->
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml:80: Warning: The resource R.integer.anim_duration_long appears to be unused [UnusedResources]
    <integer name="anim_duration_long">500</integer>      <!-- 长动画 -->
             ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\edit_text_background.xml:2: Warning: The resource R.drawable.edit_text_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\fade_in.xml:2: Warning: The resource R.anim.fade_in appears to be unused [UnusedResources]
<alpha xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\fade_out.xml:2: Warning: The resource R.anim.fade_out appears to be unused [UnusedResources]
<alpha xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_arrow_down.xml:2: Warning: The resource R.drawable.ic_arrow_down appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_arrow_left.xml:2: Warning: The resource R.drawable.ic_arrow_left appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_arrow_right.xml:2: Warning: The resource R.drawable.ic_arrow_right appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_car.xml:2: Warning: The resource R.drawable.ic_car appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_default_cover.xml:2: Warning: The resource R.drawable.ic_default_cover appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_discovery.xml:2: Warning: The resource R.drawable.ic_discovery appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_driving.xml:2: Warning: The resource R.drawable.ic_driving appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_equalizer.xml:2: Warning: The resource R.drawable.ic_equalizer appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_globe.xml:2: Warning: The resource R.drawable.ic_globe appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_grid.xml:1: Warning: The resource R.drawable.ic_grid appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_heart.xml:2: Warning: The resource R.drawable.ic_heart appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_heart_mode.xml:2: Warning: The resource R.drawable.ic_heart_mode appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_launcher_background.xml:2: Warning: The resource R.drawable.ic_launcher_background appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable-v24\ic_launcher_foreground.xml:1: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_library.xml:2: Warning: The resource R.drawable.ic_library appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_menu.xml:2: Warning: The resource R.drawable.ic_menu appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_music.xml:2: Warning: The resource R.drawable.ic_music appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_music_note.xml:1: Warning: The resource R.drawable.ic_music_note appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_needle.xml:2: Warning: The resource R.drawable.ic_needle appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_mode_level_list.xml:2: Warning: The resource R.drawable.ic_play_mode_level_list appears to be unused [UnusedResources]
<level-list xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_mode_loop.xml:2: Warning: The resource R.drawable.ic_play_mode_loop appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_mode_shuffle.xml:2: Warning: The resource R.drawable.ic_play_mode_shuffle appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_mode_single.xml:2: Warning: The resource R.drawable.ic_play_mode_single appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_order.xml:2: Warning: The resource R.drawable.ic_play_order appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_small.xml:2: Warning: The resource R.drawable.ic_play_small appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_player.xml:2: Warning: The resource R.drawable.ic_player appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_playing_play_pause_selector.xml:2: Warning: The resource R.drawable.ic_playing_play_pause_selector appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_playing_playback_progress_thumb.xml:2: Warning: The resource R.drawable.ic_playing_playback_progress_thumb appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_profile.xml:2: Warning: The resource R.drawable.ic_profile appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_random.xml:2: Warning: The resource R.drawable.ic_random appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_refresh.xml:2: Warning: The resource R.drawable.ic_refresh appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_repeat_all.xml:2: Warning: The resource R.drawable.ic_repeat_all appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_settings.xml:2: Warning: The resource R.drawable.ic_settings appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_user.xml:2: Warning: The resource R.drawable.ic_user appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_vinyl_bg.xml:2: Warning: The resource R.drawable.ic_vinyl_bg appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\item_animation_fall_down.xml:2: Warning: The resource R.anim.item_animation_fall_down appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment_header.xml:2: Warning: The resource R.layout.item_comment_header appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\layout_animation_fall_down.xml:2: Warning: The resource R.anim.layout_animation_fall_down appears to be unused [UnusedResources]
<layoutAnimation
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\login_button_background.xml:2: Warning: The resource R.drawable.login_button_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android" 
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\logo.jpg: Warning: The resource R.drawable.logo appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\next.png: Warning: The resource R.drawable.next appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\page_transition_fade.xml:2: Warning: The resource R.anim.page_transition_fade appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\pause.png: Warning: The resource R.drawable.pause appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\play.png: Warning: The resource R.drawable.play appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\player_control_bg.xml:2: Warning: The resource R.drawable.player_control_bg appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml:2: Warning: The resource R.layout.player_controls appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\previous.png: Warning: The resource R.drawable.previous appears to be unused [UnusedResources]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\reset_rotation.xml:2: Warning: The resource R.anim.reset_rotation appears to be unused [UnusedResources]
<rotate xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\rotate_album.xml:2: Warning: The resource R.anim.rotate_album appears to be unused [UnusedResources]
<rotate xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\rotate_pause_to_play.xml:2: Warning: The resource R.anim.rotate_pause_to_play appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\rotate_play_to_pause.xml:2: Warning: The resource R.anim.rotate_play_to_pause appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\round_menu_button_background.xml:2: Warning: The resource R.drawable.round_menu_button_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\scale_down.xml:2: Warning: The resource R.anim.scale_down appears to be unused [UnusedResources]
<scale xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\scale_up.xml:2: Warning: The resource R.anim.scale_up appears to be unused [UnusedResources]
<scale xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\search_background.xml:2: Warning: The resource R.drawable.search_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\sidebar_sakura_bg.xml:2: Warning: The resource R.drawable.sidebar_sakura_bg appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\splash_background.xml:2: Warning: The resource R.drawable.splash_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\splash_theme.xml:4: Warning: The resource R.style.SplashTheme appears to be unused [UnusedResources]
    <style name="SplashTheme" parent="Theme.AppCompat.NoActionBar">
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:4: Warning: The resource R.string.action_settings appears to be unused [UnusedResources]
    <string name="action_settings">Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.welcome_message appears to be unused [UnusedResources]
    <string name="welcome_message">欢迎使用智能语音音乐播放器</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.no_lyrics appears to be unused [UnusedResources]
    <string name="no_lyrics">暂无歌词</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.search_hint appears to be unused [UnusedResources]
    <string name="search_hint">输入歌曲名或歌手名</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.search appears to be unused [UnusedResources]
    <string name="search">搜索</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.recommend appears to be unused [UnusedResources]
    <string name="recommend">推荐新歌</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.toplist appears to be unused [UnusedResources]
    <string name="toplist">音乐排行榜</string>
            ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:11: Warning: The resource R.string.copyright appears to be unused [UnusedResources]
    <string name="copyright">© 2024 VoxTunes AI</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:14: Warning: The resource R.string.app_logo appears to be unused [UnusedResources]
    <string name="app_logo">轻聆 App Logo</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.app_slogan appears to be unused [UnusedResources]
    <string name="app_slogan">您的专属智能车载音乐伴侣</string>
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.qr_code_login appears to be unused [UnusedResources]
    <string name="qr_code_login">扫码登录</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:17: Warning: The resource R.string.phone_login appears to be unused [UnusedResources]
    <string name="phone_login">手机号登录</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:18: Warning: The resource R.string.guest_login appears to be unused [UnusedResources]
    <string name="guest_login">游客登录</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.version_info appears to be unused [UnusedResources]
    <string name="version_info">轻聆音乐 v1.0</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.login_successful appears to be unused [UnusedResources]
    <string name="login_successful">登录成功</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.login_failed appears to be unused [UnusedResources]
    <string name="login_failed">登录失败</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.network_error appears to be unused [UnusedResources]
    <string name="network_error">网络错误</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:23: Warning: The resource R.string.phone_number_hint appears to be unused [UnusedResources]
    <string name="phone_number_hint">请输入手机号</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.password_hint appears to be unused [UnusedResources]
    <string name="password_hint">请输入密码</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:25: Warning: The resource R.string.login appears to be unused [UnusedResources]
    <string name="login">登录</string>
            ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.cancel appears to be unused [UnusedResources]
    <string name="cancel">取消</string>
            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:27: Warning: The resource R.string.refresh_qr_code appears to be unused [UnusedResources]
    <string name="refresh_qr_code">刷新二维码</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.scan_qr_code_tip appears to be unused [UnusedResources]
    <string name="scan_qr_code_tip">请使用网易云音乐APP扫描二维码登录</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.qr_code_expired appears to be unused [UnusedResources]
    <string name="qr_code_expired">二维码已过期，请点击刷新</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.qr_code_scanned appears to be unused [UnusedResources]
    <string name="qr_code_scanned">扫描成功，请在手机上确认登录</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:31: Warning: The resource R.string.loading appears to be unused [UnusedResources]
    <string name="loading">加载中...</string>
            ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:39: Warning: The resource R.string.play appears to be unused [UnusedResources]
    <string name="play">播放</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:40: Warning: The resource R.string.pause appears to be unused [UnusedResources]
    <string name="pause">暂停</string>
            ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:41: Warning: The resource R.string.next appears to be unused [UnusedResources]
    <string name="next">下一首</string>
            ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:42: Warning: The resource R.string.previous appears to be unused [UnusedResources]
    <string name="previous">上一首</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.playlist appears to be unused [UnusedResources]
    <string name="playlist">播放列表</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:44: Warning: The resource R.string.favorite appears to be unused [UnusedResources]
    <string name="favorite">收藏</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:45: Warning: The resource R.string.unfavorite appears to be unused [UnusedResources]
    <string name="unfavorite">取消收藏</string>
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:46: Warning: The resource R.string.heart_mode appears to be unused [UnusedResources]
    <string name="heart_mode">心动模式</string>
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:47: Warning: The resource R.string.comment appears to be unused [UnusedResources]
    <string name="comment">评论</string>
            ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:50: Warning: The resource R.string.setting_auto_play appears to be unused [UnusedResources]
    <string name="setting_auto_play">自动播放</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:51: Warning: The resource R.string.setting_night_mode appears to be unused [UnusedResources]
    <string name="setting_night_mode">夜间模式</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:52: Warning: The resource R.string.setting_auto_voice_in_driving appears to be unused [UnusedResources]
    <string name="setting_auto_voice_in_driving">驾驶模式自动语音</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:62: Warning: The resource R.string.retry appears to be unused [UnusedResources]
    <string name="retry">重试</string>
            ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:63: Warning: The resource R.string.cancel_retry appears to be unused [UnusedResources]
    <string name="cancel_retry">取消</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:64: Warning: The resource R.string.network_unavailable appears to be unused [UnusedResources]
    <string name="network_unavailable">网络不可用</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:65: Warning: The resource R.string.network_restored appears to be unused [UnusedResources]
    <string name="network_restored">网络已恢复</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:77: Warning: The resource R.string.intelligence_mode appears to be unused [UnusedResources]
    <string name="intelligence_mode">心动模式</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:78: Warning: The resource R.string.intelligence_hint appears to be unused [UnusedResources]
    <string name="intelligence_hint">根据当前歌曲推荐的相似歌曲</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:79: Warning: The resource R.string.intelligence_recommendation appears to be unused [UnusedResources]
    <string name="intelligence_recommendation">推荐歌曲</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:80: Warning: The resource R.string.no_intelligence_songs appears to be unused [UnusedResources]
    <string name="no_intelligence_songs">暂无推荐歌曲</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\styles.xml:4: Warning: The resource R.style.AppButton appears to be unused [UnusedResources]
    <style name="AppButton">
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\styles.xml:16: Warning: The resource R.style.SearchEditText appears to be unused [UnusedResources]
    <style name="SearchEditText">
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\styles.xml:28: Warning: The resource R.style.PlayerControlButton appears to be unused [UnusedResources]
    <style name="PlayerControlButton">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\styles.xml:37: Warning: The resource R.style.PlayPauseButton appears to be unused [UnusedResources]
    <style name="PlayPauseButton" parent="PlayerControlButton">
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\styles.xml:45: Warning: The resource R.style.LyricTextStyle appears to be unused [UnusedResources]
    <style name="LyricTextStyle">
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\view_album_cover.xml:2: Warning: The resource R.layout.view_album_cover appears to be unused [UnusedResources]
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:31: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="loading">加载中...</string>
                           ~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml:70: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="comment_hint">说点什么吧...</string>
                                ~~~~~~~~

   Explanation for issues of type "TypographyEllipsis":
   You can replace the string "..." with a dedicated ellipsis character,
   ellipsis character (u2026, &#8230;). This can help make the text more
   readable.

   https://en.wikipedia.org/wiki/Ellipsis

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\cherry_blossom_car.jpg: Warning: Found bitmap drawable res/drawable/cherry_blossom_car.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\logo.jpg: Warning: Found bitmap drawable res/drawable/logo.jpg in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\logo_music.png: Warning: Found bitmap drawable res/drawable/logo_music.png in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\next.png: Warning: Found bitmap drawable res/drawable/next.png in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\pause.png: Warning: Found bitmap drawable res/drawable/pause.png in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\play.png: Warning: Found bitmap drawable res/drawable/play.png in densityless folder [IconLocation]
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\previous.png: Warning: Found bitmap drawable res/drawable/previous.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:175: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:185: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_comments.xml:52: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "TextFields":
   Providing an inputType attribute on a text field improves usability because
   depending on the data to be input, optimized keyboards can be shown to the
   user (such as just digits and parentheses for a phone number). 

   The lint detector also looks at the id of the view, and if the id offers a
   hint of the purpose of the field (for example, the id contains the phrase
   phone or email), then lint will also ensure that the inputType contains the
   corresponding type attributes.

   If you really want to keep the text field generic, you can suppress this
   warning by setting inputType="text".

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_online_song.xml:46: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                android:textSize="10sp"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_song.xml:63: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
        android:textSize="10sp"
        ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SmallSp":
   Avoid using sizes smaller than 11sp.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:52: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:81: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:118: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_comments.xml:52: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build.gradle:107: Information: Add suffix -ktx to enable the Kotlin extensions for this library [KtxExtensionAvailable]
    implementation 'androidx.palette:palette:1.0.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "KtxExtensionAvailable":
   Android KTX extensions augment some libraries with support for modern
   Kotlin language features like extension functions, extension properties,
   lambdas, named parameters, coroutines, and more.

   In Kotlin projects, use the KTX version of a library by replacing the
   dependency in your build.gradle file. For example, you can replace
   androidx.fragment:fragment with androidx.fragment:fragment-ktx.

   https://developer.android.com/kotlin/ktx

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ButtonAnimationUtils.kt:41: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
        view.setOnTouchListener { v, event ->
                                ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\ButtonAnimationUtils.kt:107: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
        view.setOnTouchListener { v, event ->
                                ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\login\LoginActivity.kt:120: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
            button.setOnTouchListener { v, event ->
                                      ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\player\LyricView.kt:255: Warning: Custom view LyricView overrides onTouchEvent but not performClick [ClickableViewAccessibility]
    override fun onTouchEvent(event: MotionEvent): Boolean {
                 ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\player\PlayerFragment.kt:609: Warning: Custom view `ViewPager2` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        binding.viewPagerPlayer.setOnTouchListener { _, event ->
        ^

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:37: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_player.xml:10: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:37: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:59: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:58: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:146: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageButton
                 ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:157: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageButton
                 ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:168: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageButton
                 ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:196: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageButton
                     ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:224: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageButton
                     ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_intelligence.xml:36: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:29: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment.xml:105: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment.xml:143: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment.xml:176: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_playlist.xml:16: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_reply.xml:87: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\view_album_cover.xml:8: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\view_album_cover.xml:16: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\view_album_cover.xml:25: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\CommentAdapter.kt:116: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
            binding.textLikeCount.text = comment.likeCount.toString()
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\CommentAdapter.kt:137: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                binding.textReplyCount.text = "${comment.replyCount}回复"
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\login\LoginActivity.kt:299: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        tvTip.text = "打开网易云音乐APP，点击右上角+，选择扫一扫扫描上方二维码"
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\player\PlayerFragment.kt:1001: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                countTextView.text = "(${mediaItems.size}首)"
                                     ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\adapter\ReplyAdapter.kt:127: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
            binding.textReplyLikeCount.text = reply.likeCount.toString()
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\adapter\TopListAdapter.java:41: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.songCountTextView.setText(playlist.getSongCount() + "首");
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:228: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.tvLevelTag.text = "Lv.${user.level}"
                                  ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:228: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvLevelTag.text = "Lv.${user.level}"
                                   ~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:244: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.tvLikedSongsCount.text = "268" // 这里应该使用实际数据
                                              ~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:248: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.tvPlaylistsCount.text = "32"   // 这里应该使用实际数据
                                             ~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:252: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
            binding.tvFollowedArtistsCount.text = user.follows.toString()
                                                  ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:256: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.tvListeningHours.text = "124"  // 这里应该使用实际数据
                                             ~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:268: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.tvPhone.text = "138****6789"  // 这里应该使用实际数据
                                    ~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:269: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            binding.tvEmail.text = "<EMAIL>" // 这里应该使用实际数据
                                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\profile\UserProfileFragment.kt:273: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                binding.tvVipStatus.text = "年费会员 (有效期至2024年12月)" // 这里应该使用实际数据
                                            ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:16: Warning: Hardcoded string "背景图片", should use @string resource [HardcodedText]
        android:contentDescription="背景图片" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:54: Warning: Hardcoded string "欢迎使用轻聆", should use @string resource [HardcodedText]
                android:text="欢迎使用轻聆"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:71: Warning: Hardcoded string "享受音乐，享受生活", should use @string resource [HardcodedText]
                android:text="享受音乐，享受生活"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:107: Warning: Hardcoded string "扫码登录图标", should use @string resource [HardcodedText]
                        android:contentDescription="扫码登录图标" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:114: Warning: Hardcoded string "扫码登录", should use @string resource [HardcodedText]
                        android:text="扫码登录"
                        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:146: Warning: Hardcoded string "手机登录图标", should use @string resource [HardcodedText]
                        android:contentDescription="手机登录图标" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:153: Warning: Hardcoded string "手机号登录", should use @string resource [HardcodedText]
                        android:text="手机号登录"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:184: Warning: Hardcoded string "游客登录图标", should use @string resource [HardcodedText]
                        android:contentDescription="游客登录图标" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:191: Warning: Hardcoded string "游客登录", should use @string resource [HardcodedText]
                        android:text="游客登录"
                        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml:210: Warning: Hardcoded string "轻聆音乐 v1.0", should use @string resource [HardcodedText]
                android:text="轻聆音乐 v1.0"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:72: Warning: Hardcoded string "播放器", should use @string resource [HardcodedText]
                android:contentDescription="播放器" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:97: Warning: Hardcoded string "我的音乐库", should use @string resource [HardcodedText]
                android:contentDescription="我的音乐库" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:122: Warning: Hardcoded string "音乐探索", should use @string resource [HardcodedText]
                android:contentDescription="音乐探索" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:147: Warning: Hardcoded string "驾驶模式", should use @string resource [HardcodedText]
                android:contentDescription="驾驶模式" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:172: Warning: Hardcoded string "用户中心", should use @string resource [HardcodedText]
                android:contentDescription="用户中心" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:197: Warning: Hardcoded string "设置", should use @string resource [HardcodedText]
                android:contentDescription="设置" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:220: Warning: Hardcoded string "菜单", should use @string resource [HardcodedText]
        android:contentDescription="菜单"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_player.xml:57: Warning: Hardcoded string "歌曲名称", should use @string resource [HardcodedText]
                    android:text="歌曲名称"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_player.xml:68: Warning: Hardcoded string "歌手名称", should use @string resource [HardcodedText]
                    android:text="歌手名称"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_player.xml:125: Warning: Hardcoded string "00:00", should use @string resource [HardcodedText]
                    android:text="00:00"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_player.xml:143: Warning: Hardcoded string "00:00", should use @string resource [HardcodedText]
                    android:text="00:00"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_splash.xml:36: Warning: Hardcoded string "轻聆", should use @string resource [HardcodedText]
            android:text="轻聆"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_splash.xml:50: Warning: Hardcoded string "您的专属智能车载音乐伴侣", should use @string resource [HardcodedText]
            android:text="您的专属智能车载音乐伴侣"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_splash.xml:68: Warning: Hardcoded string "Version 1.0", should use @string resource [HardcodedText]
        android:text="Version 1.0"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:25: Warning: Hardcoded string "评论", should use @string resource [HardcodedText]
            android:text="评论" />
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:45: Warning: Hardcoded string "关闭", should use @string resource [HardcodedText]
            android:contentDescription="关闭"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:69: Warning: Hardcoded string "暂无评论", should use @string resource [HardcodedText]
        android:text="暂无评论"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:122: Warning: Hardcoded string "发表评论...", should use @string resource [HardcodedText]
            android:hint="发表评论..."
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:134: Warning: Hardcoded string "发送", should use @string resource [HardcodedText]
            android:text="发送"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:149: Warning: Hardcoded string "评论发送成功", should use @string resource [HardcodedText]
        android:text="评论发送成功"
        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_heart_mode.xml:22: Warning: Hardcoded string "心动模式", should use @string resource [HardcodedText]
            android:text="心动模式"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_heart_mode.xml:39: Warning: Hardcoded string "刷新", should use @string resource [HardcodedText]
            android:contentDescription="刷新" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_heart_mode.xml:48: Warning: Hardcoded string "关闭", should use @string resource [HardcodedText]
            android:contentDescription="关闭" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_heart_mode.xml:63: Warning: Hardcoded string "根据当前歌曲为您推荐相似的音乐，开启心动模式后将自动播放这些歌曲。", should use @string resource [HardcodedText]
        android:text="根据当前歌曲为您推荐相似的音乐，开启心动模式后将自动播放这些歌曲。"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_heart_mode.xml:99: Warning: Hardcoded string "正在寻找相似歌曲...", should use @string resource [HardcodedText]
                android:text="正在寻找相似歌曲..."
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_heart_mode.xml:112: Warning: Hardcoded string "开启心动模式", should use @string resource [HardcodedText]
        android:text="开启心动模式"
        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_intelligence.xml:25: Warning: Hardcoded string "心动模式推荐", should use @string resource [HardcodedText]
            android:text="心动模式推荐" />
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_intelligence.xml:34: Warning: Hardcoded string "关闭", should use @string resource [HardcodedText]
            android:contentDescription="关闭"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_intelligence.xml:58: Warning: Hardcoded string "暂无推荐歌曲", should use @string resource [HardcodedText]
        android:text="暂无推荐歌曲"
        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:13: Warning: Hardcoded string "手机号登录", should use @string resource [HardcodedText]
        android:text="手机号登录"
        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:29: Warning: Hardcoded string "请输入手机号和密码登录", should use @string resource [HardcodedText]
            android:text="请输入手机号和密码登录"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:37: Warning: Hardcoded string "验证码登录", should use @string resource [HardcodedText]
            android:text="验证码登录"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:48: Warning: Hardcoded string "手机号码", should use @string resource [HardcodedText]
        android:text="手机号码"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:58: Warning: Hardcoded string "请输入手机号码", should use @string resource [HardcodedText]
        android:hint="请输入手机号码"
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:77: Warning: Hardcoded string "密码", should use @string resource [HardcodedText]
            android:text="密码"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:87: Warning: Hardcoded string "请输入密码", should use @string resource [HardcodedText]
            android:hint="请输入密码"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:108: Warning: Hardcoded string "验证码", should use @string resource [HardcodedText]
            android:text="验证码"
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:124: Warning: Hardcoded string "请输入验证码", should use @string resource [HardcodedText]
                android:hint="请输入验证码"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:137: Warning: Hardcoded string "获取验证码", should use @string resource [HardcodedText]
                android:text="获取验证码"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:182: Warning: Hardcoded string "取消", should use @string resource [HardcodedText]
            android:text="取消"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml:192: Warning: Hardcoded string "登录", should use @string resource [HardcodedText]
            android:text="登录"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_playlist.xml:19: Warning: Hardcoded string "播放列表", should use @string resource [HardcodedText]
            android:text="播放列表"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_playlist.xml:41: Warning: Hardcoded string "关闭", should use @string resource [HardcodedText]
            android:contentDescription="关闭"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_playlist.xml:57: Warning: Hardcoded string "播放列表为空", should use @string resource [HardcodedText]
        android:text="播放列表为空"
        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_playlist.xml:66: Warning: Hardcoded string "清空播放列表", should use @string resource [HardcodedText]
        android:text="清空播放列表"
        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:12: Warning: Hardcoded string "扫码登录", should use @string resource [HardcodedText]
        android:text="扫码登录"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:23: Warning: Hardcoded string "请使用网易云音乐APP扫描二维码登录", should use @string resource [HardcodedText]
        android:text="请使用网易云音乐APP扫描二维码登录"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:72: Warning: Hardcoded string "二维码加载失败", should use @string resource [HardcodedText]
                android:text="二维码加载失败"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:83: Warning: Hardcoded string "点击重新加载", should use @string resource [HardcodedText]
                android:text="点击重新加载"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:93: Warning: Hardcoded string "正在加载二维码...", should use @string resource [HardcodedText]
            android:text="正在加载二维码..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:105: Warning: Hardcoded string "打开网易云音乐APP，点击右上角+，选择扫一扫扫描上方二维码", should use @string resource [HardcodedText]
        android:text="打开网易云音乐APP，点击右上角+，选择扫一扫扫描上方二维码"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml:116: Warning: Hardcoded string "取消", should use @string resource [HardcodedText]
        android:text="取消"
        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:21: Warning: Hardcoded string "12:34", should use @string resource [HardcodedText]
            android:text="12:34"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:30: Warning: Hardcoded string "6月15日 周四", should use @string resource [HardcodedText]
            android:text="6月15日 周四"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:77: Warning: Hardcoded string "歌曲名称", should use @string resource [HardcodedText]
                android:text="歌曲名称"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:90: Warning: Hardcoded string "歌手名", should use @string resource [HardcodedText]
                android:text="歌手名"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:123: Warning: Hardcoded string "0:00", should use @string resource [HardcodedText]
                    android:text="0:00"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:133: Warning: Hardcoded string "0:00", should use @string resource [HardcodedText]
                    android:text="0:00"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:209: Warning: Hardcoded string "语音控制", should use @string resource [HardcodedText]
                        android:text="语音控制"
                        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml:237: Warning: Hardcoded string "退出驾驶模式", should use @string resource [HardcodedText]
                        android:text="退出驾驶模式"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_intelligence.xml:80: Warning: Hardcoded string "根据当前歌曲为您推荐的相似歌曲", should use @string resource [HardcodedText]
                android:text="根据当前歌曲为您推荐的相似歌曲"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_intelligence.xml:119: Warning: Hardcoded string "暂无相似歌曲推荐", should use @string resource [HardcodedText]
        android:text="暂无相似歌曲推荐"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_placeholder.xml:11: Warning: Hardcoded string "功能开发中...", should use @string resource [HardcodedText]
        android:text="功能开发中..."
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:14: Warning: Hardcoded string "背景", should use @string resource [HardcodedText]
        android:contentDescription="背景"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:68: Warning: Hardcoded string "唱臂", should use @string resource [HardcodedText]
                    android:contentDescription="唱臂"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:78: Warning: Hardcoded string "黑胶唱片", should use @string resource [HardcodedText]
                    android:contentDescription="黑胶唱片"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:89: Warning: Hardcoded string "专辑封面", should use @string resource [HardcodedText]
                    android:contentDescription="专辑封面"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:217: Warning: Hardcoded string "00:00", should use @string resource [HardcodedText]
                android:text="00:00" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:239: Warning: Hardcoded string "00:00", should use @string resource [HardcodedText]
                android:text="00:00" />
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:264: Warning: Hardcoded string "收藏", should use @string resource [HardcodedText]
                    android:contentDescription="收藏"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:277: Warning: Hardcoded string "上一首", should use @string resource [HardcodedText]
                    android:contentDescription="上一首"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:297: Warning: Hardcoded string "播放/暂停", should use @string resource [HardcodedText]
                        android:contentDescription="播放/暂停" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:308: Warning: Hardcoded string "下一首", should use @string resource [HardcodedText]
                    android:contentDescription="下一首"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:321: Warning: Hardcoded string "循环模式", should use @string resource [HardcodedText]
                    android:contentDescription="循环模式"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:334: Warning: Hardcoded string "播放列表", should use @string resource [HardcodedText]
                    android:contentDescription="播放列表"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:347: Warning: Hardcoded string "心动模式", should use @string resource [HardcodedText]
                    android:contentDescription="心动模式"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:360: Warning: Hardcoded string "评论", should use @string resource [HardcodedText]
                    android:contentDescription="评论"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml:373: Warning: Hardcoded string "分享", should use @string resource [HardcodedText]
                    android:contentDescription="分享"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:75: Warning: Hardcoded string "用户名", should use @string resource [HardcodedText]
                        android:text="用户名"
                        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:99: Warning: Hardcoded string "VIP会员", should use @string resource [HardcodedText]
                            android:text="VIP会员"
                            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:114: Warning: Hardcoded string "Lv.6", should use @string resource [HardcodedText]
                            android:text="Lv.6"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:127: Warning: Hardcoded string "音乐是生活的调味剂，让心灵得到治愈", should use @string resource [HardcodedText]
                        android:text="音乐是生活的调味剂，让心灵得到治愈"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:163: Warning: Hardcoded string "268", should use @string resource [HardcodedText]
                                android:text="268"
                                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:172: Warning: Hardcoded string "收藏歌曲", should use @string resource [HardcodedText]
                                android:text="收藏歌曲"
                                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:195: Warning: Hardcoded string "32", should use @string resource [HardcodedText]
                                android:text="32"
                                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:204: Warning: Hardcoded string "创建歌单", should use @string resource [HardcodedText]
                                android:text="创建歌单"
                                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:227: Warning: Hardcoded string "86", should use @string resource [HardcodedText]
                                android:text="86"
                                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:236: Warning: Hardcoded string "关注歌手", should use @string resource [HardcodedText]
                                android:text="关注歌手"
                                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:259: Warning: Hardcoded string "124", should use @string resource [HardcodedText]
                                android:text="124"
                                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:268: Warning: Hardcoded string "收听时长(小时)", should use @string resource [HardcodedText]
                                android:text="收听时长(小时)"
                                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:308: Warning: Hardcoded string "账户信息", should use @string resource [HardcodedText]
                    android:text="账户信息"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:317: Warning: Hardcoded string "更多 >", should use @string resource [HardcodedText]
                    android:text="更多 >"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:345: Warning: Hardcoded string "手机号", should use @string resource [HardcodedText]
                            android:text="手机号"
                            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:353: Warning: Hardcoded string "138**6789", should use @string resource [HardcodedText]
                            android:text="138****6789"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:373: Warning: Hardcoded string "邮箱", should use @string resource [HardcodedText]
                            android:text="邮箱"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:381: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
                            android:text="<EMAIL>"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:401: Warning: Hardcoded string "会员状态", should use @string resource [HardcodedText]
                            android:text="会员状态"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:409: Warning: Hardcoded string "年费会员 (有效期至2024年12月)", should use @string resource [HardcodedText]
                            android:text="年费会员 (有效期至2024年12月)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:429: Warning: Hardcoded string "注册时间", should use @string resource [HardcodedText]
                            android:text="注册时间"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:437: Warning: Hardcoded string "2021年03月15日", should use @string resource [HardcodedText]
                            android:text="2021年03月15日"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:451: Warning: Hardcoded string "退出登录", should use @string resource [HardcodedText]
                android:text="退出登录"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml:470: Warning: Hardcoded string "加载失败，点击重试", should use @string resource [HardcodedText]
        android:text="加载失败，点击重试"
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment.xml:153: Warning: Hardcoded string "回复", should use @string resource [HardcodedText]
                    android:text="回复"
                    ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_online_song.xml:44: Warning: Hardcoded string "VIP", should use @string resource [HardcodedText]
                android:text="VIP"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_playlist.xml:35: Warning: Hardcoded string "排行榜名称", should use @string resource [HardcodedText]
                android:text="排行榜名称"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_playlist.xml:47: Warning: Hardcoded string "排行榜描述", should use @string resource [HardcodedText]
                android:text="排行榜描述"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_playlist.xml:55: Warning: Hardcoded string "每日更新", should use @string resource [HardcodedText]
                android:text="每日更新"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_playlist_song.xml:18: Warning: Hardcoded string "歌曲封面", should use @string resource [HardcodedText]
        android:contentDescription="歌曲封面" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_reply.xml:110: Warning: Hardcoded string "回复", should use @string resource [HardcodedText]
            android:text="回复"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_search_suggest.xml:19: Warning: Hardcoded string "类型图标", should use @string resource [HardcodedText]
        android:contentDescription="类型图标"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_top_list.xml:20: Warning: Hardcoded string "排行榜封面", should use @string resource [HardcodedText]
            android:contentDescription="排行榜封面"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_comments.xml:17: Warning: Hardcoded string "评论", should use @string resource [HardcodedText]
            android:text="评论"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_comments.xml:30: Warning: Hardcoded string "(0)", should use @string resource [HardcodedText]
            android:text="(0)" />
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_comments.xml:57: Warning: Hardcoded string "添加评论...", should use @string resource [HardcodedText]
            android:hint="添加评论..."
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_comments.xml:69: Warning: Hardcoded string "发送", should use @string resource [HardcodedText]
            android:text="发送"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_playlist.xml:17: Warning: Hardcoded string "播放列表", should use @string resource [HardcodedText]
            android:text="播放列表"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_playlist.xml:30: Warning: Hardcoded string "(0首)", should use @string resource [HardcodedText]
            android:text="(0首)" />
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml:49: Warning: Hardcoded string "切换歌词", should use @string resource [HardcodedText]
            android:contentDescription="切换歌词"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml:67: Warning: Hardcoded string "00:00", should use @string resource [HardcodedText]
            android:text="00:00"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml:80: Warning: Hardcoded string "00:00", should use @string resource [HardcodedText]
            android:text="00:00"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml:97: Warning: Hardcoded string "上一首", should use @string resource [HardcodedText]
            android:contentDescription="上一首"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml:110: Warning: Hardcoded string "播放/暂停", should use @string resource [HardcodedText]
            android:contentDescription="播放/暂停"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml:121: Warning: Hardcoded string "下一首", should use @string resource [HardcodedText]
            android:contentDescription="下一首"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\view_lottie_loading.xml:25: Warning: Hardcoded string "加载中...", should use @string resource [HardcodedText]
        android:text="加载中..."
        ~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml:27: Warning: @id/text_comment_count can overlap @id/button_comment_close if @id/text_comment_count grows due to localized text expansion [RelativeOverlap]
        <TextView
         ~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_playlist.xml:23: Warning: @id/text_playlist_count can overlap @id/button_playlist_close if @id/text_playlist_count grows due to localized text expansion [RelativeOverlap]
        <TextView
         ~~~~~~~~

   Explanation for issues of type "RelativeOverlap":
   If relative layout has text or button items aligned to left and right sides
   they can overlap each other due to localized text expansion unless they
   have mutual constraints like toEndOf/toStartOf.

C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:214: Warning: Consider replacing android:layout_alignParentLeft with android:layout_alignParentStart="true" to better support right-to-left layouts [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml:216: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="16dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginLeft="16dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlHardcoded":
   Using Gravity#LEFT and Gravity#RIGHT can lead to problems when a layout is
   rendered in locales where text flows from right to left. Use Gravity#START
   and Gravity#END instead. Similarly, in XML gravity and layout_gravity
   attributes, use start rather than left.

   For XML attributes such as paddingLeft and layout_marginLeft, use
   paddingStart and layout_marginStart. NOTE: If your minSdkVersion is less
   than 17, you should add both the older left/right attributes as well as the
   new start/end attributes. On older platforms, where RTL is not supported
   and the start/end attributes are unknown and therefore ignored, you need
   the older left/right attributes. There is a separate lint check which
   catches that type of error.

   (Note: For Gravity#LEFT and Gravity#START, you can use these constants even
   when targeting older platforms, because the start bitmask is a superset of
   the left bitmask. Therefore, you can use gravity="start" rather than
   gravity="left|start".)

27 errors, 566 warnings
