<dependencies>
  <compile
      roots="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar:unspecified@jar,__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar:unspecified@jar,:@@:app::debug,androidx.databinding:viewbinding:8.8.0@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,com.google.dagger:hilt-android:2.50@aar,com.karumi:dexter:6.2.3@aar,androidx.navigation:navigation-common:2.7.5@aar,androidx.navigation:navigation-common-ktx:2.7.5@aar,androidx.navigation:navigation-runtime:2.7.5@aar,androidx.navigation:navigation-runtime-ktx:2.7.5@aar,androidx.navigation:navigation-fragment:2.7.5@aar,androidx.navigation:navigation-fragment-ktx:2.7.5@aar,androidx.navigation:navigation-ui-ktx:2.7.5@aar,androidx.navigation:navigation-ui:2.7.5@aar,com.google.android.material:material:1.11.0@aar,androidx.appcompat:appcompat:1.6.1@aar,com.journeyapps:zxing-android-embedded:4.2.0@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,com.github.bumptech.glide:okhttp3-integration:4.16.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment-ktx:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.activity:activity-ktx:1.8.0@aar,androidx.activity:activity:1.8.0@aar,androidx.palette:palette:1.0.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.recyclerview:recyclerview:1.3.0@aar,androidx.recyclerview:recyclerview:1.3.0@aar,androidx.transition:transition:1.4.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.media:media:1.6.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.customview:customview:1.1.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.9.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.9.0@aar,androidx.core:core:1.9.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.cardview:cardview:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.collection:collection:1.2.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.annotation:annotation:1.5.0@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,androidx.media3:media3-exoplayer:1.2.1@aar,androidx.media3:media3-session:1.2.1@aar,androidx.media3:media3-common:1.2.1@aar,androidx.annotation:annotation-experimental:1.3.1@aar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:converter-scalars:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,androidx.media3:media3-datasource-okhttp:1.2.1@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.google.code.gson:gson:2.10.1@jar,androidx.media3:media3-ui:1.2.1@aar,androidx.multidex:multidex:2.0.1@aar,jp.wasabeef:glide-transformations:4.3.0@aar,com.google.zxing:core:3.4.1@jar,com.airbnb.android:lottie:6.1.0@aar,de.hdodenhof:circleimageview:3.1.0@aar,junit:junit:4.13.2@jar,org.jetbrains:annotations:23.0.0@jar,org.hamcrest:hamcrest-core:1.3@jar,com.google.guava:guava:31.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.tracing:tracing:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.exifinterface:exifinterface:1.3.6@aar,com.google.guava:failureaccess:1.0.1@jar,androidx.media3:media3-container:1.2.1@aar,androidx.media3:media3-database:1.2.1@aar,androidx.media3:media3-datasource:1.2.1@aar,androidx.media3:media3-decoder:1.2.1@aar,androidx.media3:media3-extractor:1.2.1@aar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,com.google.dagger:hilt-core:2.50@jar,com.google.dagger:dagger:2.50@jar,javax.inject:javax.inject:1@jar,com.google.dagger:dagger-lint-aar:2.50@aar,com.google.code.findbugs:jsr305:3.0.2@jar">
    <dependency
        name="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar:unspecified@jar"
        simpleName="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar"/>
    <dependency
        name="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar:unspecified@jar"
        simpleName="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar"/>
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.0@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="com.google.dagger:hilt-android:2.50@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="com.karumi:dexter:6.2.3@aar"
        simpleName="com.karumi:dexter"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.5@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.5@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.7.5@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-fragment-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-fragment-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-ui-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.7.5@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.11.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.journeyapps:zxing-android-embedded:4.2.0@aar"
        simpleName="com.journeyapps:zxing-android-embedded"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="com.github.bumptech.glide:okhttp3-integration:4.16.0@aar"
        simpleName="com.github.bumptech.glide:okhttp3-integration"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.palette:palette:1.0.0@aar"
        simpleName="androidx.palette:palette"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.media:media:1.6.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.9.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.9.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.annotation:annotation:1.5.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.2.1@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.media3:media3-session:1.2.1@aar"
        simpleName="androidx.media3:media3-session"/>
    <dependency
        name="androidx.media3:media3-common:1.2.1@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:converter-scalars:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-scalars"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="androidx.media3:media3-datasource-okhttp:1.2.1@aar"
        simpleName="androidx.media3:media3-datasource-okhttp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="androidx.media3:media3-ui:1.2.1@aar"
        simpleName="androidx.media3:media3-ui"/>
    <dependency
        name="androidx.multidex:multidex:2.0.1@aar"
        simpleName="androidx.multidex:multidex"/>
    <dependency
        name="jp.wasabeef:glide-transformations:4.3.0@aar"
        simpleName="jp.wasabeef:glide-transformations"/>
    <dependency
        name="com.google.zxing:core:3.4.1@jar"
        simpleName="com.google.zxing:core"/>
    <dependency
        name="com.airbnb.android:lottie:6.1.0@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="de.hdodenhof:circleimageview:3.1.0@aar"
        simpleName="de.hdodenhof:circleimageview"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="androidx.media3:media3-container:1.2.1@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-database:1.2.1@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-datasource:1.2.1@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.2.1@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-extractor:1.2.1@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.dagger:hilt-core:2.50@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.50@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.50@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
  </compile>
  <package
      roots="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar:unspecified@jar,__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar:unspecified@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,junit:junit:4.13.2@jar,androidx.databinding:viewbinding:8.8.0@aar,com.karumi:dexter:6.2.3@aar,androidx.navigation:navigation-common:2.7.5@aar,androidx.navigation:navigation-common-ktx:2.7.5@aar,androidx.navigation:navigation-runtime:2.7.5@aar,androidx.navigation:navigation-runtime-ktx:2.7.5@aar,androidx.navigation:navigation-fragment:2.7.5@aar,androidx.navigation:navigation-fragment-ktx:2.7.5@aar,androidx.navigation:navigation-ui-ktx:2.7.5@aar,androidx.navigation:navigation-ui:2.7.5@aar,com.google.android.material:material:1.11.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.airbnb.android:lottie:6.1.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,com.google.dagger:hilt-android:2.50@aar,com.journeyapps:zxing-android-embedded:4.2.0@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,com.github.bumptech.glide:okhttp3-integration:4.16.0@aar,jp.wasabeef:glide-transformations:4.3.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment-ktx:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.activity:activity-ktx:1.8.0@aar,androidx.activity:activity:1.8.0@aar,androidx.media3:media3-ui:1.2.1@aar,androidx.media3:media3-session:1.2.1@aar,androidx.media3:media3-datasource-okhttp:1.2.1@aar,androidx.media3:media3-extractor:1.2.1@aar,androidx.media3:media3-container:1.2.1@aar,androidx.media3:media3-datasource:1.2.1@aar,androidx.media3:media3-decoder:1.2.1@aar,androidx.media3:media3-database:1.2.1@aar,androidx.media3:media3-common:1.2.1@aar,androidx.media3:media3-exoplayer:1.2.1@aar,androidx.palette:palette:1.0.0@aar,androidx.emoji2:emoji2-views-helper:1.2.0@aar,androidx.emoji2:emoji2:1.2.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.recyclerview:recyclerview:1.3.0@aar,androidx.recyclerview:recyclerview:1.3.0@aar,androidx.media:media:1.6.0@aar,androidx.media:media:1.6.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.9.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.customview:customview:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.transition:transition:1.4.1@aar,androidx.window:window:1.0.0@aar,androidx.core:core:1.9.0@aar,androidx.core:core:1.9.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,androidx.annotation:annotation-experimental:1.3.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.collection:collection:1.2.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.profileinstaller:profileinstaller:1.3.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.annotation:annotation:1.5.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:converter-scalars:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar,com.google.code.gson:gson:2.10.1@jar,androidx.multidex:multidex:2.0.1@aar,com.google.zxing:core:3.4.1@jar,de.hdodenhof:circleimageview:3.1.0@aar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,com.google.errorprone:error_prone_annotations:2.15.0@jar,androidx.constraintlayout:constraintlayout-core:1.0.4@jar,com.google.guava:guava:31.1-android@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,com.google.dagger:hilt-core:2.50@jar,com.google.dagger:dagger:2.50@jar,com.google.dagger:dagger-lint-aar:2.50@aar,com.google.code.findbugs:jsr305:3.0.2@jar,javax.inject:javax.inject:1@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar">
    <dependency
        name="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar:unspecified@jar"
        simpleName="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar"/>
    <dependency
        name="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar:unspecified@jar"
        simpleName="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.0@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="com.karumi:dexter:6.2.3@aar"
        simpleName="com.karumi:dexter"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.5@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.5@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.7.5@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-fragment-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-fragment-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-ui-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.7.5@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.11.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.airbnb.android:lottie:6.1.0@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.dagger:hilt-android:2.50@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="com.journeyapps:zxing-android-embedded:4.2.0@aar"
        simpleName="com.journeyapps:zxing-android-embedded"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="com.github.bumptech.glide:okhttp3-integration:4.16.0@aar"
        simpleName="com.github.bumptech.glide:okhttp3-integration"/>
    <dependency
        name="jp.wasabeef:glide-transformations:4.3.0@aar"
        simpleName="jp.wasabeef:glide-transformations"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.media3:media3-ui:1.2.1@aar"
        simpleName="androidx.media3:media3-ui"/>
    <dependency
        name="androidx.media3:media3-session:1.2.1@aar"
        simpleName="androidx.media3:media3-session"/>
    <dependency
        name="androidx.media3:media3-datasource-okhttp:1.2.1@aar"
        simpleName="androidx.media3:media3-datasource-okhttp"/>
    <dependency
        name="androidx.media3:media3-extractor:1.2.1@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="androidx.media3:media3-container:1.2.1@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-datasource:1.2.1@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.2.1@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-database:1.2.1@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-common:1.2.1@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.2.1@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.palette:palette:1.0.0@aar"
        simpleName="androidx.palette:palette"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.media:media:1.6.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.9.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.window:window:1.0.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.core:core:1.9.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation:1.5.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:converter-scalars:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-scalars"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="androidx.multidex:multidex:2.0.1@aar"
        simpleName="androidx.multidex:multidex"/>
    <dependency
        name="com.google.zxing:core:3.4.1@jar"
        simpleName="com.google.zxing:core"/>
    <dependency
        name="de.hdodenhof:circleimageview:3.1.0@aar"
        simpleName="de.hdodenhof:circleimageview"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.dagger:hilt-core:2.50@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.50@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.50@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
