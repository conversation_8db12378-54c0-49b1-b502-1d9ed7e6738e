package com.example.aimusicplayer.utils

import android.content.Context
import android.util.Log
import com.example.aimusicplayer.api.ApiManager
import com.example.aimusicplayer.utils.NetworkResult
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.data.repository.UserRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 功能测试工具类
 * 用于验证应用的核心功能是否正常工作
 * 特别适合在开发和调试阶段使用
 */
@Singleton
class FunctionalityTester @Inject constructor(
    private val context: Context,
    private val apiManager: ApiManager,
    private val musicRepository: MusicRepository,
    private val userRepository: UserRepository
) {
    companion object {
        private const val TAG = "FunctionalityTester"
    }

    /**
     * 运行所有功能测试
     */
    fun runAllTests() {
        Log.i(TAG, "开始运行功能测试...")

        CoroutineScope(Dispatchers.IO).launch {
            try {
                testNetworkConnectivity()
                testApiManager()
                testMusicRepository()
                testUserRepository()
                testDatabaseConnection()

                withContext(Dispatchers.Main) {
                    Log.i(TAG, "所有功能测试完成")
                }
            } catch (e: Exception) {
                Log.e(TAG, "功能测试出错", e)
            }
        }
    }

    /**
     * 测试网络连接
     */
    private suspend fun testNetworkConnectivity() = withContext(Dispatchers.IO) {
        Log.d(TAG, "测试网络连接...")

        try {
            val isNetworkAvailable = NetworkUtils.isNetworkAvailable(context)
            if (isNetworkAvailable) {
                Log.i(TAG, "✅ 网络连接正常")
            } else {
                Log.w(TAG, "❌ 网络连接不可用")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 网络连接测试失败", e)
        }
    }

    /**
     * 测试API管理器
     */
    private suspend fun testApiManager() = withContext(Dispatchers.IO) {
        Log.d(TAG, "测试API管理器...")

        try {
            val apiService = apiManager.apiService
            if (apiService != null) {
                Log.i(TAG, "✅ API管理器初始化正常")

                // 测试网络可用性检查
                val isNetworkAvailable = apiManager.isNetworkAvailable()
                Log.i(TAG, "网络可用性: $isNetworkAvailable")
            } else {
                Log.e(TAG, "❌ API管理器未正确初始化")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ API管理器测试失败", e)
        }
    }

    /**
     * 测试音乐仓库
     */
    private suspend fun testMusicRepository() = withContext(Dispatchers.IO) {
        Log.d(TAG, "测试音乐仓库...")

        try {
            // 测试获取新歌速递
            Log.d(TAG, "测试获取新歌速递...")
            val newSongs = musicRepository.getNewSongs()
            Log.i(TAG, "✅ 获取新歌速递成功，数量: ${newSongs.size}")

            // 测试搜索功能
            Log.d(TAG, "测试搜索功能...")
            val searchResults = musicRepository.searchSongs("周杰伦")
            when (searchResults) {
                is NetworkResult.Success -> {
                    Log.i(TAG, "✅ 搜索功能正常，结果数量: ${searchResults.data.size}")
                }
                is NetworkResult.Error -> {
                    Log.w(TAG, "⚠️ 搜索功能返回错误: ${searchResults.message}")
                }
                is NetworkResult.Loading -> {
                    Log.i(TAG, "ℹ️ 搜索功能加载中")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ 音乐仓库测试失败", e)
        }
    }

    /**
     * 测试用户仓库
     */
    private suspend fun testUserRepository() = withContext(Dispatchers.IO) {
        Log.d(TAG, "测试用户仓库...")

        try {
            // 测试用户状态检查
            val isLoggedIn = userRepository.isLoggedIn()
            Log.i(TAG, "用户登录状态: $isLoggedIn")

            if (isLoggedIn) {
                val userInfo = userRepository.currentUserFlow.value
                if (userInfo != null) {
                    Log.i(TAG, "✅ 用户信息获取成功: ${userInfo.username}")
                } else {
                    Log.w(TAG, "⚠️ 用户已登录但无法获取用户信息")
                }
            } else {
                Log.i(TAG, "ℹ️ 用户未登录")
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ 用户仓库测试失败", e)
        }
    }

    /**
     * 测试数据库连接
     */
    private suspend fun testDatabaseConnection() = withContext(Dispatchers.IO) {
        Log.d(TAG, "测试数据库连接...")

        try {
            // 测试获取收藏歌曲数量
            val favoriteSongs = musicRepository.getLikedSongs()
            favoriteSongs.collect { result ->
                when (result) {
                    is NetworkResult.Success -> {
                        Log.i(TAG, "✅ 数据库连接正常，收藏歌曲数量: ${result.data.size}")
                    }
                    is NetworkResult.Error -> {
                        Log.w(TAG, "⚠️ 获取收藏歌曲失败: ${result.message}")
                    }
                    is NetworkResult.Loading -> {
                        Log.i(TAG, "ℹ️ 正在加载收藏歌曲...")
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ 数据库连接测试失败", e)
        }
    }

    /**
     * 测试特定功能
     */
    fun testSpecificFunction(functionName: String) {
        Log.i(TAG, "测试特定功能: $functionName")

        CoroutineScope(Dispatchers.IO).launch {
            try {
                when (functionName) {
                    "network" -> testNetworkConnectivity()
                    "api" -> testApiManager()
                    "music" -> testMusicRepository()
                    "user" -> testUserRepository()
                    "database" -> testDatabaseConnection()
                    else -> Log.w(TAG, "未知的功能测试: $functionName")
                }
            } catch (e: Exception) {
                Log.e(TAG, "特定功能测试失败: $functionName", e)
            }
        }
    }

    /**
     * 生成测试报告
     */
    fun generateTestReport(): TestReport {
        return TestReport(
            timestamp = System.currentTimeMillis(),
            networkStatus = NetworkUtils.isNetworkAvailable(context),
            apiManagerStatus = try { apiManager.apiService != null } catch (e: Exception) { false },
            userLoginStatus = try { userRepository.isLoggedIn() } catch (e: Exception) { false }
        )
    }

    /**
     * 测试报告数据类
     */
    data class TestReport(
        val timestamp: Long,
        val networkStatus: Boolean,
        val apiManagerStatus: Boolean,
        val userLoginStatus: Boolean
    ) {
        fun toLogString(): String {
            return """
                |=== 功能测试报告 ===
                |时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date(timestamp))}
                |网络状态: ${if (networkStatus) "✅ 正常" else "❌ 异常"}
                |API管理器: ${if (apiManagerStatus) "✅ 正常" else "❌ 异常"}
                |用户登录: ${if (userLoginStatus) "✅ 已登录" else "ℹ️ 未登录"}
                |==================
            """.trimMargin()
        }
    }
}
