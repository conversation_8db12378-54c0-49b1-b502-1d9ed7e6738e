package com.example.aimusicplayer.service

import androidx.annotation.StringRes
import com.example.aimusicplayer.R

/**
 * 播放模式
 * 定义了播放器的各种播放模式
 */
sealed class PlayMode(val value: Int, @StringRes val nameRes: Int) {
    object Loop : PlayMode(0, R.string.play_mode_loop)
    object Shuffle : PlayMode(1, R.string.play_mode_shuffle)
    object Single : PlayMode(2, R.string.play_mode_single)

    companion object {
        fun valueOf(value: Int): PlayMode {
            return when (value) {
                0 -> Loop
                1 -> Shuffle
                2 -> Single
                else -> Loop
            }
        }

        /**
         * 获取所有播放模式的数组（Java兼容）
         * @return 播放模式数组
         */
        @JvmStatic
        fun values(): Array<PlayMode> {
            return arrayOf(Loop, Shuffle, Single)
        }

        /**
         * 获取播放模式的序号（Java兼容）
         * @param mode 播放模式
         * @return 序号
         */
        @JvmStatic
        fun ordinal(mode: PlayMode): Int {
            return mode.value
        }
    }

    /**
     * 获取当前播放模式的序号（Java兼容）
     * @return 序号
     */
    fun ordinal(): Int {
        return value
    }
}
