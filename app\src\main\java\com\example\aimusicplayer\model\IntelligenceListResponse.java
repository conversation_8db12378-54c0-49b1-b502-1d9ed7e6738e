package com.example.aimusicplayer.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 心动模式推荐歌曲响应
 */
public class IntelligenceListResponse {

    @SerializedName("code")
    private int code;

    @SerializedName("data")
    private List<IntelligenceItem> data;

    public int getCode() {
        return code;
    }

    public List<IntelligenceItem> getData() {
        return data;
    }

    /**
     * 心动模式推荐歌曲项
     */
    public static class IntelligenceItem {

        @SerializedName("songInfo")
        private SongInfo songInfo;

        public SongInfo getSongInfo() {
            return songInfo;
        }

        /**
         * 歌曲信息
         */
        public static class SongInfo {

            @SerializedName("id")
            private long id;

            @SerializedName("name")
            private String name;

            @SerializedName("ar")
            private List<Artist> artists;

            @SerializedName("al")
            private Album album;

            @SerializedName("dt")
            private long duration;

            public long getId() {
                return id;
            }

            public String getName() {
                return name;
            }

            public List<Artist> getArtists() {
                return artists;
            }

            public Album getAlbum() {
                return album;
            }

            public long getDuration() {
                return duration;
            }

            /**
             * 获取艺术家名称（多个艺术家用逗号分隔）
             */
            public String getArtistNames() {
                if (artists == null || artists.isEmpty()) {
                    return "未知艺术家";
                }

                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < artists.size(); i++) {
                    if (i > 0) {
                        sb.append(", ");
                    }
                    sb.append(artists.get(i).getName());
                }
                return sb.toString();
            }
        }

        /**
         * 艺术家信息
         */
        public static class Artist {
            @SerializedName("id")
            private long id;

            @SerializedName("name")
            private String name;

            public long getId() {
                return id;
            }

            public String getName() {
                return name != null ? name : "未知艺术家";
            }
        }

        /**
         * 专辑信息
         */
        public static class Album {
            @SerializedName("id")
            private long id;

            @SerializedName("name")
            private String name;

            @SerializedName("picUrl")
            private String picUrl;

            public long getId() {
                return id;
            }

            public String getName() {
                return name != null ? name : "未知专辑";
            }

            public String getPicUrl() {
                return picUrl;
            }
        }
    }
}
