package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.api.ApiManager
import com.example.aimusicplayer.api.UnifiedApiService
import com.example.aimusicplayer.data.model.UserDetailResponse
import com.example.aimusicplayer.data.repository.UserRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.model.LoginStatus
import com.example.aimusicplayer.data.model.User
import com.example.aimusicplayer.ui.main.SidebarController
import com.example.aimusicplayer.utils.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import javax.inject.Inject

/**
 * 主页面的ViewModel
 * 负责处理主页面的业务逻辑
 * 使用Flow管理UI状态
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    application: Application,
    private val userRepository: UserRepository,
    private val apiService: UnifiedApiService,
    private val apiManager: ApiManager,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    companion object {
        private const val TAG = "MainViewModel"
    }

    // 用户信息的StateFlow
    private val _userDataFlow = MutableStateFlow<User?>(null)
    val userDataFlow: StateFlow<User?> = _userDataFlow.asStateFlow()
    val userData: LiveData<User?> = userDataFlow.asLiveData() // 兼容LiveData

    // 当前选中的导航项的StateFlow
    private val _selectedNavItemFlow = MutableStateFlow<Int>(0)
    val selectedNavItemFlow: StateFlow<Int> = _selectedNavItemFlow.asStateFlow()
    val selectedNavItem: LiveData<Int> = selectedNavItemFlow.asLiveData() // 兼容LiveData

    // 侧边栏控制器
    private val sidebarController = SidebarController()

    // 登录状态的StateFlow
    private val _loginStatusFlow = MutableStateFlow<LoginStatus?>(null)
    val loginStatusFlow: StateFlow<LoginStatus?> = _loginStatusFlow.asStateFlow()
    val loginStatus: LiveData<LoginStatus?> = loginStatusFlow.asLiveData() // 兼容LiveData

    // 用户详情的StateFlow
    private val _userDetailFlow = MutableStateFlow<UserDetailResponse?>(null)
    val userDetailFlow: StateFlow<UserDetailResponse?> = _userDetailFlow.asStateFlow()
    val userDetail: LiveData<UserDetailResponse?> = userDetailFlow.asLiveData() // 兼容LiveData

    // 退出登录结果的StateFlow
    private val _logoutResultFlow = MutableStateFlow<Boolean?>(null)
    val logoutResultFlow: StateFlow<Boolean?> = _logoutResultFlow.asStateFlow()
    val logoutResult: LiveData<Boolean?> = logoutResultFlow.asLiveData() // 兼容LiveData

    init {
        // 加载用户数据
        loadUserData()
    }

    /**
     * 获取侧边栏状态的LiveData
     */
    fun getIsDrawerOpen(): LiveData<Boolean> {
        return sidebarController.getIsVisible()
    }

    /**
     * 设置当前选中的导航项
     */
    fun setSelectedNavItem(position: Int) {
        _selectedNavItemFlow.value = position
    }

    /**
     * 设置侧边栏状态
     */
    fun setDrawerOpen(isOpen: Boolean) {
        sidebarController.setVisible(isOpen)
    }

    /**
     * 切换侧边栏状态
     */
    fun toggleDrawer() {
        sidebarController.toggleSidebar()
    }

    /**
     * 初始化侧边栏控制器
     * @param sidebarNav 侧边栏视图
     * @param btnMenuRight 菜单按钮
     * @param fragmentContainer Fragment容器
     * @param navButtons 导航按钮数组
     */
    fun initializeSidebarController(
        sidebarNav: LinearLayout,
        btnMenuRight: ImageView,
        fragmentContainer: FrameLayout,
        navButtons: Array<View>
    ) {
        sidebarController.initializeViews(sidebarNav, btnMenuRight, fragmentContainer, navButtons)
    }

    /**
     * 安排侧边栏自动隐藏
     */
    fun scheduleSidebarAutoHide() {
        sidebarController.scheduleAutoHide()
    }

    /**
     * 加载用户数据
     */
    private fun loadUserData() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载用户数据失败", e)
            }
        ) {
            try {
                // 从UserRepository获取用户数据
                val isLoggedIn = userRepository.isLoggedIn()
                if (isLoggedIn) {
                    val userId = userRepository.getUserId()
                    val username = userRepository.getUsername()
                    val token = userRepository.getUserToken()
                    val avatarUrl = userRepository.getAvatarUrl()

                    val user = User(userId, username, token, avatarUrl)
                    _userDataFlow.value = user

                    Log.d(TAG, "加载用户数据成功: $username")
                } else {
                    Log.d(TAG, "用户未登录")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载用户数据失败", e)
                throw e
            }
        }
    }

    /**
     * 判断当前用户是否为游客登录
     * @return 如果是游客登录返回true，否则返回false
     */
    fun isGuestUser(): Boolean {
        // 检查用户是否已登录
        if (!userRepository.isLoggedIn()) {
            return false // 未登录
        }

        // 获取用户名，游客用户名通常以"游客"开头
        val username = userRepository.getUsername()
        return username != null && username.startsWith("游客")
    }

    /**
     * 检查登录状态
     */
    fun checkLoginStatus() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "检查登录状态失败", e)
                _loginStatusFlow.value = LoginStatus(false, null, null, null)
            }
        ) {
            setLoading(true)
            try {
                // 调用API检查登录状态
                val response = withContext(Dispatchers.IO) {
                    userRepository.checkLoginStatus()
                }

                // 解析响应
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    // 登录状态检查成功
                    val data = jsonObject.optJSONObject("data")

                    if (data != null) {
                        val account = data.optJSONObject("account")
                        val profile = data.optJSONObject("profile")

                        if (account != null) {
                            val userId = account.optString("id", "")
                            var nickname = ""
                            var avatarUrl = ""

                            if (profile != null) {
                                nickname = profile.optString("nickname", "")
                                avatarUrl = profile.optString("avatarUrl", "")
                            }

                            // 保存用户信息
                            if (userId.isNotEmpty()) {
                                userRepository.saveLoginStatus(true, "user_token", userId, nickname)

                                // 创建登录状态对象
                                val loginStatus = LoginStatus(true, userId, nickname, avatarUrl)
                                _loginStatusFlow.value = loginStatus
                            } else {
                                // 用户ID为空，视为未登录
                                val loginStatus = LoginStatus(false, null, null, null)
                                _loginStatusFlow.value = loginStatus
                            }
                        } else {
                            // 数据为空，视为未登录
                            val loginStatus = LoginStatus(false, null, null, null)
                            _loginStatusFlow.value = loginStatus
                        }
                    } else {
                        // 未登录
                        val loginStatus = LoginStatus(false, null, null, null)
                        _loginStatusFlow.value = loginStatus
                    }
                } else {
                    // API返回错误码
                    val msg = jsonObject.optString("msg", "登录状态检查失败: 状态码 $code")
                    handleError(Exception(msg), msg)
                    val loginStatus = LoginStatus(false, null, null, null)
                    _loginStatusFlow.value = loginStatus
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 获取用户ID
     * @return 用户ID，如果未登录则返回空字符串
     */
    fun getUserId(): String {
        return userRepository.getUserId()
    }

    /**
     * 获取用户名
     * @return 用户名，如果未登录则返回空字符串
     */
    fun getUsername(): String {
        return userRepository.getUsername()
    }

    /**
     * 获取用户详情
     * @param userId 用户ID
     */
    fun getUserDetail(userId: String) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "获取用户详情失败", e)
                _userDetailFlow.value = null
            }
        ) {
            setLoading(true)
            try {
                // 调用API获取用户详情
                val result = withContext(Dispatchers.IO) {
                    userRepository.getUserDetail(userId)
                }

                when (val networkResult = result) {
                    is NetworkResult.Success -> {
                        _userDetailFlow.value = networkResult.data
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(networkResult.message), networkResult.message)
                        _userDetailFlow.value = null
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                    }
                    else -> {
                        // 可选：处理其他未知情况或记录日志
                        Log.w(TAG, "Unhandled NetworkResult state: $networkResult")
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 退出登录
     */
    fun logout() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "退出登录失败", e)
                handleError(e, "退出登录失败: ${e.message}")
                _logoutResultFlow.value = false
            }
        ) {
            setLoading(true)
            try {
                // 调用UserRepository清除登录状态
                withContext(Dispatchers.IO) {
                    userRepository.logout()
                }

                // 清除用户数据
                _userDataFlow.value = null
                _logoutResultFlow.value = true

                Log.d(TAG, "退出登录成功")
            } finally {
                setLoading(false)
            }
        }
    }
}
