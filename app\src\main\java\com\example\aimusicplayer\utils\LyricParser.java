package com.example.aimusicplayer.utils;

import android.text.TextUtils;
import android.util.Log;

import com.example.aimusicplayer.data.model.LyricLine;
import com.example.aimusicplayer.data.model.LyricInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 歌词解析器
 * 支持解析LRC格式的歌词文件
 */
public class LyricParser {

    private static final String TAG = "LyricParser";

    // 时间标签正则表达式
    private static final Pattern PATTERN_TIME_TAG = Pattern.compile("\\[(\\d{2}):(\\d{2})\\.(\\d{2,3})\\]");

    // 元数据标签正则表达式
    private static final Pattern PATTERN_METADATA = Pattern.compile("\\[(\\w+):(.+?)\\]");

    /**
     * 解析LRC格式的歌词
     * @param lrcContent LRC歌词内容
     * @return 歌词信息
     */
    public static LyricInfo parseLrc(String lrcContent) {
        if (TextUtils.isEmpty(lrcContent)) {
            return null;
        }

        String title = "";
        String artist = "";
        String album = "";
        String lyricist = "";
        String composer = "";
        List<LyricLine> entries = new ArrayList<>();

        // 按行分割歌词
        String[] lines = lrcContent.split("\\r?\\n");

        for (String line : lines) {
            // 解析元数据
            Matcher metadataMatcher = PATTERN_METADATA.matcher(line);
            while (metadataMatcher.find()) {
                String key = metadataMatcher.group(1);
                String value = metadataMatcher.group(2);

                if (key != null && value != null) {
                    switch (key.toLowerCase()) {
                        case "ti":
                            title = value;
                            break;
                        case "ar":
                            artist = value;
                            break;
                        case "al":
                            album = value;
                            break;
                        case "by":
                            lyricist = value;
                            break;
                        case "co":
                            composer = value;
                            break;
                    }
                }
            }

            // 解析时间标签和歌词文本
            Matcher timeMatcher = PATTERN_TIME_TAG.matcher(line);
            List<Long> times = new ArrayList<>();

            // 提取所有时间标签
            while (timeMatcher.find()) {
                int minutes = Integer.parseInt(timeMatcher.group(1));
                int seconds = Integer.parseInt(timeMatcher.group(2));
                int milliseconds;

                String msStr = timeMatcher.group(3);
                if (msStr.length() == 2) {
                    milliseconds = Integer.parseInt(msStr) * 10;
                } else {
                    milliseconds = Integer.parseInt(msStr);
                }

                long time = minutes * 60 * 1000 + seconds * 1000 + milliseconds;
                times.add(time);
            }

            // 提取歌词文本
            String text = line.replaceAll("\\[\\d{2}:\\d{2}\\.\\d{2,3}\\]", "").trim();

            // 为每个时间标签创建一个歌词条目
            for (Long time : times) {
                if (!text.isEmpty()) {
                    entries.add(new LyricLine(time, text, null));
                }
            }
        }

        // 按时间排序
        Collections.sort(entries, new Comparator<LyricLine>() {
            @Override
            public int compare(LyricLine o1, LyricLine o2) {
                return Long.compare(o1.getTime(), o2.getTime());
            }
        });

        // 创建歌词信息
        LyricInfo lyricInfo = new LyricInfo(title, artist, album, lyricist, composer, new ArrayList<>(entries), false);

        return lyricInfo;
    }

    /**
     * 解析带翻译的LRC格式歌词
     * @param originalLrc 原文歌词内容
     * @param translatedLrc 翻译歌词内容
     * @return 歌词信息
     */
    public static LyricInfo parseWithTranslation(String originalLrc, String translatedLrc) {
        // 解析原文歌词
        LyricInfo originalInfo = parseLrc(originalLrc);
        if (originalInfo == null || originalInfo.isEmpty()) {
            return null;
        }

        // 如果没有翻译歌词，直接返回原文歌词
        if (TextUtils.isEmpty(translatedLrc)) {
            return originalInfo;
        }

        // 解析翻译歌词
        LyricInfo translatedInfo = parseLrc(translatedLrc);
        if (translatedInfo == null || translatedInfo.isEmpty()) {
            return originalInfo;
        }

        // 合并原文和翻译歌词
        List<LyricLine> originalEntries = originalInfo.getLyricEntries();
        List<LyricLine> translatedEntries = translatedInfo.getLyricEntries();
        List<LyricLine> mergedEntries = new ArrayList<>();

        // 为原文歌词添加翻译
        for (LyricLine originalEntry : originalEntries) {
            long time = originalEntry.getTime();

            // 查找最接近的翻译歌词
            LyricLine closestEntry = findClosestEntry(translatedEntries, time);
            String translation = closestEntry != null ? closestEntry.getText() : null;

            // 创建带翻译的新歌词行
            mergedEntries.add(new LyricLine(time, originalEntry.getText(), translation));
        }

        // 创建新的歌词信息
        return new LyricInfo(
            originalInfo.getTitle(),
            originalInfo.getArtist(),
            originalInfo.getAlbum(),
            originalInfo.getLyricist(),
            originalInfo.getComposer(),
            mergedEntries,
            true
        );
    }

    /**
     * 查找最接近指定时间的歌词条目
     * @param entries 歌词条目列表
     * @param time 时间
     * @return 最接近的歌词条目
     */
    private static LyricLine findClosestEntry(List<LyricLine> entries, long time) {
        if (entries == null || entries.isEmpty()) {
            return null;
        }

        LyricLine closestEntry = null;
        long minDiff = Long.MAX_VALUE;

        for (LyricLine entry : entries) {
            long diff = Math.abs(entry.getTime() - time);
            if (diff < minDiff) {
                minDiff = diff;
                closestEntry = entry;
            }
        }

        // 如果时间差太大，认为没有匹配的翻译
        if (minDiff > 500) {
            return null;
        }

        return closestEntry;
    }

    /**
     * 将歌词信息转换为LRC格式的字符串
     * @param lyricInfo 歌词信息
     * @return LRC格式的字符串
     */
    public static String toLrcString(LyricInfo lyricInfo) {
        if (lyricInfo == null || lyricInfo.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        // 添加元数据
        if (lyricInfo.getTitle() != null) {
            sb.append("[ti:").append(lyricInfo.getTitle()).append("]\n");
        }
        if (lyricInfo.getArtist() != null) {
            sb.append("[ar:").append(lyricInfo.getArtist()).append("]\n");
        }
        if (lyricInfo.getAlbum() != null) {
            sb.append("[al:").append(lyricInfo.getAlbum()).append("]\n");
        }
        if (lyricInfo.getLyricist() != null) {
            sb.append("[by:").append(lyricInfo.getLyricist()).append("]\n");
        }
        if (lyricInfo.getComposer() != null) {
            sb.append("[co:").append(lyricInfo.getComposer()).append("]\n");
        }

        // 添加歌词条目
        for (LyricLine entry : lyricInfo.getLyricEntries()) {
            sb.append(formatTime(entry.getTime())).append(entry.getText()).append("\n");
        }

        return sb.toString();
    }

    /**
     * 格式化时间为LRC格式的时间标签
     * @param time 时间（毫秒）
     * @return LRC格式的时间标签
     */
    private static String formatTime(long time) {
        long minutes = time / 60000;
        long seconds = (time % 60000) / 1000;
        long milliseconds = time % 1000;

        return String.format("[%02d:%02d.%03d]", minutes, seconds, milliseconds);
    }
}
