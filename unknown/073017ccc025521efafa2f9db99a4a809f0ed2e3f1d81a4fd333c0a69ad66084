package com.example.aimusicplayer.ui.discovery;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.adapter.PlaylistAdapter;
import com.example.aimusicplayer.api.ApiManager;
import com.example.aimusicplayer.api.ApiResponse;
import com.example.aimusicplayer.api.UnifiedApiService;
import com.example.aimusicplayer.data.model.Song;
import com.example.aimusicplayer.data.model.Artist;
import com.example.aimusicplayer.data.model.Album;
import com.example.aimusicplayer.model.PlaylistDetailResponse;
import com.example.aimusicplayer.model.TopListResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 排行榜Fragment
 * 显示网易云音乐的各种排行榜
 * 使用Hilt依赖注入
 */
@AndroidEntryPoint
public class TopListFragment extends Fragment implements PlaylistAdapter.OnPlaylistClickListener {

    private static final String TAG = "TopListFragment";

    private RecyclerView recyclerView;
    private ProgressBar progressBar;
    private PlaylistAdapter adapter;
    private List<TopListResponse.PlayList> playlists = new ArrayList<>();
    private OnSongListLoadedListener listener;

    @Inject
    ApiManager apiManager;

    @Inject
    UnifiedApiService apiService;

    // 接口：用于将加载的歌曲列表传回给Activity
    public interface OnSongListLoadedListener {
        void onSongListLoaded(List<Song> songList, String playlistName);
    }

    public void setOnSongListLoadedListener(OnSongListLoadedListener listener) {
        this.listener = listener;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_top_list, container, false);
        recyclerView = view.findViewById(R.id.recyclerView);
        progressBar = view.findViewById(R.id.progressBar);

        // 设置RecyclerView
        setupRecyclerView();

        // 加载排行榜
        loadTopLists();

        return view;
    }

    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new PlaylistAdapter(playlists, this);
        recyclerView.setAdapter(adapter);
    }

    private void loadTopLists() {
        progressBar.setVisibility(View.VISIBLE);

        // 使用注入的apiManager执行API调用
        Call<TopListResponse> call = apiService.getTopLists();
        apiManager.execute(call, new Callback<TopListResponse>() {
            @Override
            public void onResponse(Call<TopListResponse> call, Response<TopListResponse> response) {
                progressBar.setVisibility(View.GONE);

                if (response.isSuccessful() && response.body() != null) {
                    TopListResponse topListResponse = response.body();
                    if (topListResponse.getCode() == 200 && topListResponse.getList() != null) {
                        playlists.clear();
                        playlists.addAll(topListResponse.getList());
                        adapter.notifyDataSetChanged();
                    } else {
                        Toast.makeText(getContext(), "获取排行榜列表失败", Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "获取排行榜API错误：" + topListResponse.getCode());
                    }
                } else {
                    Toast.makeText(getContext(), "获取排行榜请求失败", Toast.LENGTH_SHORT).show();
                    Log.e(TAG, "获取排行榜请求失败：" + (response.errorBody() != null ? response.errorBody().toString() : "未知错误"));
                }
            }

            @Override
            public void onFailure(Call<TopListResponse> call, Throwable t) {
                progressBar.setVisibility(View.GONE);
                Toast.makeText(getContext(), "网络错误: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                Log.e(TAG, "获取排行榜网络错误", t);
            }
        });
    }

    @Override
    public void onPlaylistClick(int position) {
        if (position >= 0 && position < playlists.size()) {
            TopListResponse.PlayList playlist = playlists.get(position);
            loadPlaylistSongs(playlist.getId(), playlist.getName());
        }
    }

    private void loadPlaylistSongs(long playlistId, String playlistName) {
        progressBar.setVisibility(View.VISIBLE);

        // 使用注入的apiManager执行API调用
        Call<PlaylistDetailResponse> call = apiService.getPlaylistDetail(String.valueOf(playlistId));
        apiManager.execute(call, new Callback<PlaylistDetailResponse>() {
            @Override
            public void onResponse(Call<PlaylistDetailResponse> call, Response<PlaylistDetailResponse> response) {
                progressBar.setVisibility(View.GONE);

                if (response.isSuccessful() && response.body() != null) {
                    PlaylistDetailResponse playlistResponse = response.body();
                    if (playlistResponse.getCode() == 200 && playlistResponse.getPlaylist() != null &&
                            playlistResponse.getPlaylist().getTracks() != null) {

                        List<Song> songList = new ArrayList<>();
                        List<PlaylistDetailResponse.Track> tracks = playlistResponse.getPlaylist().getTracks();
                        List<PlaylistDetailResponse.Privilege> privileges = playlistResponse.getPlaylist().getPrivileges();

                        // 处理歌曲列表
                        for (int i = 0; i < tracks.size(); i++) {
                            PlaylistDetailResponse.Track track = tracks.get(i);

                            // 构建艺术家名称
                            StringBuilder artistBuilder = new StringBuilder();
                            if (track.getArtists() != null && !track.getArtists().isEmpty()) {
                                for (int j = 0; j < track.getArtists().size(); j++) {
                                    if (j > 0) artistBuilder.append("/");
                                    artistBuilder.append(track.getArtists().get(j).getName());
                                }
                            }

                            // 获取专辑信息
                            String albumName = "";
                            String albumCoverUrl = "";
                            if (track.getAlbum() != null) {
                                albumName = track.getAlbum().getName();
                                albumCoverUrl = track.getAlbum().getPicUrl();
                            }

                            // 检查VIP状态
                            boolean isVip = false;
                            if (privileges != null && i < privileges.size()) {
                                PlaylistDetailResponse.Privilege privilege = privileges.get(i);
                                isVip = privilege.isVip();
                            }

                            // 创建艺术家列表
                            List<Artist> artists = new ArrayList<>();
                            if (track.getArtists() != null && !track.getArtists().isEmpty()) {
                                for (PlaylistDetailResponse.Artist artist : track.getArtists()) {
                                    // 使用正确的Artist构造函数：id, name, translations, aliases
                                    artists.add(new Artist(artist.getId(), artist.getName(), null, null));
                                }
                            }

                            // 创建专辑对象
                            Album album = null;
                            if (track.getAlbum() != null) {
                                // 使用Album的默认构造函数，然后设置属性
                                album = new Album();
                                album.setId(String.valueOf(track.getAlbum().getId()));
                                album.setName(track.getAlbum().getName());
                                album.setCoverUrl(track.getAlbum().getPicUrl());
                                album.setPicUrl(track.getAlbum().getPicUrl());
                            }

                            Song song = new Song(
                                    track.getId(),
                                    track.getName(),
                                    artists,
                                    album,
                                    track.getDuration(),
                                    0L, // publishTime
                                    albumCoverUrl, // picUrl
                                    null // song
                            );

                            songList.add(song);
                        }

                        // 通知Activity更新歌曲列表
                        if (listener != null) {
                            listener.onSongListLoaded(songList, playlistName);
                        }
                    } else {
                        Toast.makeText(getContext(), "获取歌单详情失败", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Toast.makeText(getContext(), "获取歌单详情请求失败", Toast.LENGTH_SHORT).show();
                    Log.e(TAG, "获取歌单详情请求失败: " + (response.errorBody() != null ? response.errorBody().toString() : "未知错误"));
                }
            }

            @Override
            public void onFailure(Call<PlaylistDetailResponse> call, Throwable t) {
                progressBar.setVisibility(View.GONE);
                Toast.makeText(getContext(), "网络错误: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                Log.e(TAG, "获取歌单详情网络错误", t);
            }
        });
    }
}
