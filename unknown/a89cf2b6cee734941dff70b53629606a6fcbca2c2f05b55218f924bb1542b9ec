package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * Banner数据模型
 */
data class Banner(
    var id: String = "",
    var imageUrl: String = "",
    var targetUrl: String = "",
    var title: String = "",
    var type: Int = 0, // 1: 歌曲, 2: 专辑, 3: 歌单, 4: 外部链接
    var targetId: String = "",

    @SerializedName("titleColor")
    var titleColor: String = "",

    @SerializedName("typeTitle")
    var typeTitle: String = "",

    @SerializedName("url")
    var url: String = "",

    @SerializedName("targetType")
    var targetType: Int = 0
) {

}
