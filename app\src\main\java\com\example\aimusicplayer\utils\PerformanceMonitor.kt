package com.example.aimusicplayer.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.text.DecimalFormat
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 性能监控工具类
 * 用于监控应用的内存使用、CPU使用等性能指标
 * 特别适合车载场景的性能监控
 */
@Singleton
class PerformanceMonitor @Inject constructor(
    private val context: Context
) {
    companion object {
        const val TAG = "PerformanceMonitor"
        private const val MONITOR_INTERVAL = 5000L // 5秒监控一次
        private const val MB = 1024 * 1024
    }

    private val decimalFormat = DecimalFormat("#.##")
    private var monitoringJob: Job? = null
    private var isMonitoring = false

    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.w(TAG, "性能监控已经在运行中")
            return
        }

        isMonitoring = true
        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            while (isActive && isMonitoring) {
                try {
                    logPerformanceMetrics()
                    delay(MONITOR_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "性能监控出错", e)
                }
            }
        }
        Log.i(TAG, "性能监控已启动")
    }

    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
        Log.i(TAG, "性能监控已停止")
    }

    /**
     * 记录性能指标
     */
    private fun logPerformanceMetrics() {
        val memoryInfo = getMemoryInfo()
        val cpuInfo = getCpuInfo()

        Log.i(TAG, "=== 性能监控报告 ===")
        Log.i(TAG, "内存使用: ${memoryInfo.usedMemoryMB}MB / ${memoryInfo.totalMemoryMB}MB (${memoryInfo.usagePercentage}%)")
        Log.i(TAG, "可用内存: ${memoryInfo.availableMemoryMB}MB")
        Log.i(TAG, "堆内存: ${memoryInfo.heapUsedMB}MB / ${memoryInfo.heapMaxMB}MB")
        Log.i(TAG, "CPU使用率: ${cpuInfo.cpuUsage}%")
        Log.i(TAG, "==================")
    }

    /**
     * 获取内存信息
     */
    fun getMemoryInfo(): MemoryInfo {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)

        val totalMemoryMB = memoryInfo.totalMem / MB
        val availableMemoryMB = memoryInfo.availMem / MB
        val usedMemoryMB = totalMemoryMB - availableMemoryMB
        val usagePercentage = (usedMemoryMB.toFloat() / totalMemoryMB * 100).toInt()

        // 获取堆内存信息
        val runtime = Runtime.getRuntime()
        val heapMaxMB = runtime.maxMemory() / MB
        val heapTotalMB = runtime.totalMemory() / MB
        val heapFreeMB = runtime.freeMemory() / MB
        val heapUsedMB = heapTotalMB - heapFreeMB

        return MemoryInfo(
            totalMemoryMB = totalMemoryMB,
            availableMemoryMB = availableMemoryMB,
            usedMemoryMB = usedMemoryMB,
            usagePercentage = usagePercentage,
            heapMaxMB = heapMaxMB,
            heapTotalMB = heapTotalMB,
            heapUsedMB = heapUsedMB,
            isLowMemory = memoryInfo.lowMemory
        )
    }

    /**
     * 获取CPU信息
     */
    fun getCpuInfo(): CpuInfo {
        // 简单的CPU使用率估算
        // 在实际应用中，可以使用更精确的方法
        val cpuUsage = estimateCpuUsage()

        return CpuInfo(
            cpuUsage = cpuUsage
        )
    }

    /**
     * 估算CPU使用率
     */
    private fun estimateCpuUsage(): Float {
        // 这是一个简化的CPU使用率估算
        // 实际应用中可以使用更精确的方法，如读取/proc/stat
        return try {
            val startTime = System.nanoTime()
            val startCpuTime = Debug.threadCpuTimeNanos()

            // 短暂等待
            Thread.sleep(100)

            val endTime = System.nanoTime()
            val endCpuTime = Debug.threadCpuTimeNanos()

            val cpuTime = endCpuTime - startCpuTime
            val totalTime = endTime - startTime

            val usage = (cpuTime.toFloat() / totalTime * 100)
            decimalFormat.format(usage).toFloat()
        } catch (e: Exception) {
            Log.w(TAG, "无法获取CPU使用率", e)
            0f
        }
    }

    /**
     * 检查是否为低内存状态
     */
    fun isLowMemory(): Boolean {
        return getMemoryInfo().isLowMemory
    }

    /**
     * 获取内存使用百分比
     */
    fun getMemoryUsagePercentage(): Int {
        return getMemoryInfo().usagePercentage
    }

    /**
     * 记录方法执行时间
     */
    inline fun <T> measureTime(methodName: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        val result = block()
        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime

        Log.d(TAG, "方法 $methodName 执行时间: ${duration}ms")

        if (duration > 100) {
            Log.w(TAG, "方法 $methodName 执行时间较长: ${duration}ms")
        }

        return result
    }

    /**
     * 内存信息数据类
     */
    data class MemoryInfo(
        val totalMemoryMB: Long,
        val availableMemoryMB: Long,
        val usedMemoryMB: Long,
        val usagePercentage: Int,
        val heapMaxMB: Long,
        val heapTotalMB: Long,
        val heapUsedMB: Long,
        val isLowMemory: Boolean
    )

    /**
     * CPU信息数据类
     */
    data class CpuInfo(
        val cpuUsage: Float
    )
}
