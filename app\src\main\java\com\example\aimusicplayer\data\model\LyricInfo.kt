package com.example.aimusicplayer.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 歌词信息
 * 包含歌曲信息和歌词条目列表
 */
@Parcelize
data class LyricInfo(
    // 歌曲标题
    var title: String = "",

    // 歌手
    var artist: String = "",

    // 专辑
    var album: String = "",

    // 歌词作者
    var lyricist: String = "",

    // 作曲家
    var composer: String = "",

    // 歌词条目列表
    var entries: MutableList<LyricLine> = mutableListOf(),

    // 是否有翻译歌词
    var hasTranslation: Boolean = false
) : Parcelable {

    /**
     * 添加歌词条目
     * @param entry 歌词条目
     */
    fun addEntry(entry: LyricLine) {
        entries.add(entry)

        // 检查是否有翻译歌词
        if (entry.hasTranslation()) {
            hasTranslation = true
        }
    }

    /**
     * 设置歌词条目列表
     * @param lyricLines 歌词条目列表
     */
    fun setLyricEntries(lyricLines: List<LyricLine>) {
        this.entries.clear()
        this.entries.addAll(lyricLines)

        // 检查是否有翻译歌词
        for (entry in lyricLines) {
            if (entry.hasTranslation()) {
                hasTranslation = true
                break
            }
        }
    }

    /**
     * 获取歌词条目数量
     * @return 歌词条目数量
     */
    fun size(): Int {
        return entries.size
    }

    /**
     * 是否为空
     * @return 是否为空
     */
    fun isEmpty(): Boolean {
        return entries.isEmpty()
    }

    /**
     * 获取歌词条目列表
     * @return 歌词条目列表
     */
    fun getLyricEntries(): List<LyricLine> {
        return entries
    }

    /**
     * 判断是否有翻译歌词
     * @return 是否有翻译歌词
     */
    fun hasTranslation(): Boolean {
        return hasTranslation
    }

    /**
     * 根据时间查找歌词行索引
     * @param time 时间（毫秒）
     * @return 歌词行索引，如果没有找到则返回-1
     */
    fun findLineIndexByTime(time: Long): Int {
        if (entries.isEmpty()) return -1

        // 如果当前位置小于第一行时间，返回-1
        if (time < entries.first().time) return -1

        // 如果当前位置大于最后一行时间，返回最后一行
        if (time >= entries.last().time) return entries.size - 1

        // 二分查找当前行
        var left = 0
        var right = entries.size - 1

        while (left <= right) {
            val mid = (left + right) / 2

            if (mid + 1 < entries.size && time >= entries[mid].time && time < entries[mid + 1].time) {
                return mid
            } else if (time < entries[mid].time) {
                right = mid - 1
            } else {
                left = mid + 1
            }
        }

        return left - 1
    }


}
