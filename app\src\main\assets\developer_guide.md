# 轻聆音乐播放器 - 开发者指南

## 项目概述

轻聆音乐播放器是一个基于MVVM架构的Android音乐播放应用，专为车载场景优化。项目采用Kotlin和Java混合开发，使用最新的Android开发技术栈。

## 架构设计

### 整体架构
- **架构模式**: MVVM (Model-View-ViewModel)
- **依赖注入**: Hilt
- **数据流管理**: Kotlin Flow + LiveData兼容层
- **网络请求**: Retrofit + OkHttp
- **数据库**: Room
- **图片加载**: Glide
- **播放器**: ExoPlayer (Media3)

### 目录结构
```
app/src/main/java/com/example/aimusicplayer/
├── api/                    # API接口和响应处理
├── data/                   # 数据层
│   ├── db/                # 数据库相关
│   ├── model/             # 数据模型 (Kotlin)
│   ├── repository/        # 仓库层
│   └── source/            # 数据源
├── error/                 # 错误处理
├── model/                 # 兼容模型 (Java)
├── service/               # 服务层
├── ui/                    # UI层
├── utils/                 # 工具类
└── viewmodel/             # ViewModel层
```

## 最近修改记录

### 2024年修复和优化

#### 1. 编译错误全面修复
**问题**: 多个编译错误导致项目无法构建
**解决方案**:
- **PlayerViewModel.kt**: 修复了`fromDataModelLyricInfo`方法不存在和类型转换错误
- **MusicDataSource.kt**: 将`apiService`访问权限从private改为internal
- **MusicRepository.kt**: 添加了缺失的导入（Log、User），修复了PlayList构造函数参数类型错误
- **DrivingModeViewModel.kt**: 修复了FlowViewModel导入路径错误，添加了缺失的StateFlow定义
- **缓存机制**: 修复了clearPlaylistCache方法中的缓存访问错误

**修改文件**:
- `app/src/main/java/com/example/aimusicplayer/model/LyricInfo.java`
- `app/src/main/java/com/example/aimusicplayer/viewmodel/PlayerViewModel.kt`
- `app/src/main/java/com/example/aimusicplayer/data/source/MusicDataSource.kt`
- `app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt`
- `app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt`

#### 2. 搜索功能实现
**功能**: 实现了音乐搜索功能
**实现内容**:
- 完善了MusicRepository中的searchSongs方法
- 添加了JSON解析逻辑，支持从API响应中提取歌曲信息
- 实现了歌曲、艺术家、专辑信息的完整解析

**修改文件**:
- `app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt`

#### 3. 收藏歌单功能
**功能**: 实现了用户收藏歌单获取功能
**实现内容**:
- 完善了getCollectedPlaylists方法
- 添加了缓存机制
- 预留了本地数据库集成接口

**修改文件**:
- `app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt`

#### 4. 编译错误详细修复记录
**具体修复内容**:
1. **apiService访问权限**: 从private改为internal，解决MusicRepository无法访问的问题
2. **类型转换错误**: 修复PlayList构造函数中Long/String类型不匹配问题
3. **缺失导入**: 添加Log、User等必要的导入声明
4. **方法不存在**: 在LyricInfo.java中添加fromDataModelLyricInfo静态方法
5. **StateFlow定义**: 在DrivingModeViewModel中添加_voiceCommandResultFlow定义
6. **缓存访问**: 修复clearPlaylistCache方法中的缓存键访问逻辑
7. **导入路径**: 修正FlowViewModel的导入路径
8. **NetworkResult.Loading**: 将Loading从class改为object，修复所有使用Loading()的地方为Loading

#### 4. 收藏歌单API集成
**功能**: 正确实现了收藏歌单功能，调用相关API接口
**实现内容**:
- 根据api.txt文档实现了getUserPlaylists方法，调用`/user/playlist`接口
- 实现了subscribePlaylist方法，调用`/playlist/subscribe`接口进行收藏/取消收藏
- 添加了完整的JSON解析逻辑，正确处理歌单信息、创建者信息等
- 实现了缓存机制和缓存清理功能
- 在ApiService接口中添加了缺失的API方法

**修改文件**:
- `app/src/main/java/com/example/aimusicplayer/data/repository/MusicRepository.kt`
- `app/src/main/java/com/example/aimusicplayer/data/source/ApiService.kt`

#### 5. 语音控制功能（待开发）
**状态**: 暂时注释，待后续开发
**说明**:
- 语音控制相关代码已注释，避免编译错误
- 保留了完整的实现框架，便于后续开发
- 当前返回"语音控制功能正在开发中，敬请期待"

**修改文件**:
- `app/src/main/java/com/example/aimusicplayer/viewmodel/DrivingModeViewModel.kt`

## 核心功能模块

### 1. 播放器核心 (PlayerViewModel)
- **功能**: 音乐播放控制、歌词显示、专辑封面处理
- **特色**:
  - 支持歌词拖拽定位
  - 专辑封面旋转动画
  - 背景模糊效果
  - 颜色提取

### 2. 音乐库管理 (MusicRepository)
- **功能**: 音乐数据获取、缓存管理、搜索功能
- **特色**:
  - 智能缓存机制
  - 网络和本地数据统一管理
  - 支持相似歌曲推荐

### 3. 驾驶模式 (DrivingModeViewModel)
- **功能**: 车载场景优化、语音控制
- **特色**:
  - 自然语言语音命令解析
  - 音量控制集成
  - 播放模式切换

### 4. 用户界面
- **设计**: 横屏优化、大屏触摸友好
- **特色**:
  - 无标题栏全屏模式
  - 流畅动画效果
  - 车载场景UI适配

## 开发规范

### 1. 代码风格
- Kotlin优先，Java兼容
- 使用Flow作为主要数据流，提供LiveData兼容层
- 所有ViewModel继承FlowViewModel
- 所有Repository继承BaseRepository

### 2. 错误处理
- 统一使用GlobalErrorHandler
- 网络请求使用NetworkResult封装
- 异常信息用户友好化

### 3. 缓存策略
- API响应缓存5分钟
- 图片缓存使用Glide管理
- 歌词缓存本地存储

### 4. 性能优化
- 使用协程处理异步操作
- 图片加载优化
- 减少主线程阻塞

## API集成

### 网易云音乐API
- **基础URL**: https://1355831898-4499wupl9z.ap-guangzhou.tencentscf.com
- **认证**: Cookie-based
- **缓存**: 2分钟缓存机制
- **跨域**: 支持withCredentials

### 主要接口
- 搜索: `/search`
- 歌曲详情: `/song/detail`
- 歌词: `/lyric`
- 新歌速递: `/personalized/newsong`
- 相似歌曲: `/simi/song`
- 用户歌单: `/user/playlist`
- 收藏歌单: `/playlist/subscribe`
- 歌单详情: `/playlist/detail`

## 数据库设计

### 核心表结构
- **songs**: 歌曲信息表
- **playlists**: 播放列表表
- **play_history**: 播放历史表
- **user_preferences**: 用户设置表

## 待完成功能

### 高优先级
1. **语音控制功能**: 完善语音识别和命令执行功能
2. **播放器控制器集成**: 将PlayerController集成到各个功能模块中
3. **用户登录状态管理**: 完善用户认证和状态管理
4. **本地音乐扫描**: 实现本地音乐文件扫描和管理

### 中优先级
1. **歌单管理**: 完善用户歌单的创建、编辑、删除功能
2. **下载功能**: 实现音乐下载和离线播放
3. **主题系统**: 实现多主题切换

### 低优先级
1. **社交功能**: 评论、分享等社交功能增强
2. **个性化推荐**: 基于用户行为的智能推荐
3. **桌面小部件**: Android桌面小部件支持

## 测试指南

### 单元测试
- ViewModel测试使用MockK
- Repository测试模拟网络响应
- 工具类测试覆盖边界情况

### 集成测试
- API接口测试
- 数据库操作测试
- 播放器功能测试

### UI测试
- 关键用户流程测试
- 车载场景适配测试
- 语音控制功能测试

## 部署和发布

### 构建配置
- 支持ARM和x86架构
- 混淆配置优化
- 签名配置管理

### 版本管理
- 语义化版本号
- 变更日志维护
- 发布流程自动化

---

**最后更新**: 2024年
**维护者**: 开发团队
**联系方式**: 项目仓库Issues
