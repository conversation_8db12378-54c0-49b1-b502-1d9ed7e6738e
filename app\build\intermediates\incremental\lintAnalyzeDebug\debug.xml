<variant
    name="debug"
    package="com.example.aimusicplayer"
    minSdkVersion="24"
    targetSdkVersion="34"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.8.0"
    partialResultsDir="build\intermediates\lint_partial_results\debug\lintAnalyzeDebug\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad80fde52d7e0484dc23fb8fa90aaa23\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      viewBinding="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\debug\java;src\main\kotlin;src\debug\kotlin"
        resDirectories="src\main\res;src\debug\res"
        assetsDirectories="src\main\assets;src\debug\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\debug\compileDebugJavaWithJavac\classes;build\tmp\kotlin-classes\debug;build\intermediates\hilt\component_classes\debug;build\generated\ksp\debug\resources;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\debug\processDebugResources\R.jar"
      type="MAIN"
      applicationId="com.example.aimusicplayer"
      generatedSourceFolders="build\generated\source\navigation-args\debug;build\generated\ksp\debug\java;build\generated\ksp\debug\kotlin;build\generated\ap_generated_sources\debug\out;build\generated\data_binding_base_class_source_out\debug\out"
      generatedResourceFolders="build\generated\res\resValues\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad80fde52d7e0484dc23fb8fa90aaa23\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
