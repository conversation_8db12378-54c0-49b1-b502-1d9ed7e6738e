package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 歌单数据模型
 */
data class PlayList(
    var id: String = "",
    var name: String = "",

    @SerializedName("coverImgUrl")
    var coverImgUrl: String = "",

    var description: String = "",
    var creatorId: String = "",
    var creatorName: String = "",
    var songCount: Int = 0,
    var playCount: Int = 0,
    var subscribed: Boolean = false,
    var songs: MutableList<Song> = mutableListOf()
) {
    /**
     * 获取封面URL
     */
    fun getCoverUrl(): String {
        return coverImgUrl
    }

    /**
     * 设置封面URL
     */
    fun setCoverUrl(url: String) {
        coverImgUrl = url
    }




}
