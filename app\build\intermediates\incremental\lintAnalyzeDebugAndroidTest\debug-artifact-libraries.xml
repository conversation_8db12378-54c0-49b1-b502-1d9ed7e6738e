<libraries>
  <library
      name="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar:unspecified@jar"
      jars="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar"
      resolved="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\bdasr_V3_20210628_cfe8c44.jar:unspecified"
      provided="true"/>
  <library
      name="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar:unspecified@jar"
      jars="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar"
      resolved="__local_aars__:C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\libs\com.baidu.tts_2.6.2.2.20200629_44818d4.jar:unspecified"
      provided="true"/>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.test.ext:junit:1.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7767f3deafaf64a7f05bd134c732b675\transformed\junit-1.1.5\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7767f3deafaf64a7f05bd134c732b675\transformed\junit-1.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28186663fba1b3e1abf4a125844ad71b\transformed\espresso-core-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28186663fba1b3e1abf4a125844ad71b\transformed\espresso-core-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.databinding:viewbinding:8.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6544556cf1674494534f8900bce21c07\transformed\viewbinding-8.8.0\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6544556cf1674494534f8900bce21c07\transformed\viewbinding-8.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="com.google.dagger:hilt-android:2.50@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8dbd27110790d4450617052df3c5d93\transformed\hilt-android-2.50\jars\classes.jar"
      resolved="com.google.dagger:hilt-android:2.50"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8dbd27110790d4450617052df3c5d93\transformed\hilt-android-2.50"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b859d16da5f3352acdbdf665c8c5318a\transformed\core-1.5.0\jars\classes.jar"
      resolved="androidx.test:core:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b859d16da5f3352acdbdf665c8c5318a\transformed\core-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.karumi:dexter:6.2.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\jars\classes.jar"
      resolved="com.karumi:dexter:6.2.3"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3e619597e4c9cd618216bba15d9c65\transformed\navigation-common-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d3e619597e4c9cd618216bba15d9c65\transformed\navigation-common-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6cb25c545573f419954e68b8ad06ac8\transformed\navigation-common-ktx-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6cb25c545573f419954e68b8ad06ac8\transformed\navigation-common-ktx-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6775c4e0d2ccafaa99b9198dc5c7bbc7\transformed\navigation-runtime-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6775c4e0d2ccafaa99b9198dc5c7bbc7\transformed\navigation-runtime-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3c414797dbc39db6a242631605ef8e6\transformed\navigation-runtime-ktx-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3c414797dbc39db6a242631605ef8e6\transformed\navigation-runtime-ktx-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-fragment:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\752d364363cfe83d07ebd08f1853b21e\transformed\navigation-fragment-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-fragment:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\752d364363cfe83d07ebd08f1853b21e\transformed\navigation-fragment-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-fragment-ktx:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3929c15ecd3ed3bcbd327761aaa90f30\transformed\navigation-fragment-ktx-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-fragment-ktx:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3929c15ecd3ed3bcbd327761aaa90f30\transformed\navigation-fragment-ktx-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-ui-ktx:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be6552077492e037cb648ba9d20c50b2\transformed\navigation-ui-ktx-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-ui-ktx:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be6552077492e037cb648ba9d20c50b2\transformed\navigation-ui-ktx-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-ui:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f4bdb46ff28ab587a34c53d6687bf99\transformed\navigation-ui-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-ui:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f4bdb46ff28ab587a34c53d6687bf99\transformed\navigation-ui-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.11.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83271d0c85512209c52890db5dc246bd\transformed\material-1.11.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.11.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83271d0c85512209c52890db5dc246bd\transformed\material-1.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92121f0061d84fd6603428dd9555221c\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92121f0061d84fd6603428dd9555221c\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.journeyapps:zxing-android-embedded:4.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\jars\classes.jar"
      resolved="com.journeyapps:zxing-android-embedded:4.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42d76bf5c058c9faeb6229240c83266\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42d76bf5c058c9faeb6229240c83266\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:okhttp3-integration:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:okhttp3-integration:4.16.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd8b03a9aacee39a4f36b236dc8c1b64\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd8b03a9aacee39a4f36b236dc8c1b64\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af85d7b532fa32807b254d6290ddfca0\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af85d7b532fa32807b254d6290ddfca0\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\268dd332085b36047c7c8b393feff7e2\transformed\fragment-ktx-1.6.2\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\268dd332085b36047c7c8b393feff7e2\transformed\fragment-ktx-1.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23cf6da45ccd670ee21b434999efbd41\transformed\fragment-1.6.2\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23cf6da45ccd670ee21b434999efbd41\transformed\fragment-1.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea0d4e8623464af29dca49a272472225\transformed\activity-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea0d4e8623464af29dca49a272472225\transformed\activity-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d303f1f0d1dfdf89d203456ad79266\transformed\activity-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d303f1f0d1dfdf89d203456ad79266\transformed\activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.palette:palette:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b92fa3efd2e831e33070469b4d31f135\transformed\palette-1.0.0\jars\classes.jar"
      resolved="androidx.palette:palette:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b92fa3efd2e831e33070469b4d31f135\transformed\palette-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18e927d56ef561df1431f9c8adb12ddc\transformed\appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18e927d56ef561df1431f9c8adb12ddc\transformed\appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af754651ec5d146d4692fd33a60cd6b5\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af754651ec5d146d4692fd33a60cd6b5\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a822c4c17d161f53845a19fb128f54e\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a822c4c17d161f53845a19fb128f54e\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5877e86b535d8beee8141860616f120d\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5877e86b535d8beee8141860616f120d\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52ecf52e2c51c09fa887ff15e8044d4a\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52ecf52e2c51c09fa887ff15e8044d4a\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082fda2880df5324d73d21838f7bf0aa\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082fda2880df5324d73d21838f7bf0aa\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b127eabf3fe07a7bca176f62ec5e72c\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b127eabf3fe07a7bca176f62ec5e72c\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bf41a125552eeedf1dc74cafa954fee\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bf41a125552eeedf1dc74cafa954fee\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb84340a329e1aeccfdae9447d9038e0\transformed\media-1.0.0\jars\classes.jar"
      resolved="androidx.media:media:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb84340a329e1aeccfdae9447d9038e0\transformed\media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\422bad9bd1fca6b358e7b95759e82cab\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\422bad9bd1fca6b358e7b95759e82cab\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b355037f12f0dbc6daa1b4ef416f622\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b355037f12f0dbc6daa1b4ef416f622\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a572985955c753a461ab551d4cdef229\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a572985955c753a461ab551d4cdef229\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b859aed4ff87e5f7f2564514b13edb6\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b859aed4ff87e5f7f2564514b13edb6\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25a5afb01185fcabb33098703e48850c\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25a5afb01185fcabb33098703e48850c\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\518fe953358109ce0c8063a6625d5c4f\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\518fe953358109ce0c8063a6625d5c4f\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\452a3d61bd5a4df9c7b891c073bc51a8\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\452a3d61bd5a4df9c7b891c073bc51a8\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d18e326a7c95950bec51507cc408934\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d18e326a7c95950bec51507cc408934\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424fca0debd1544e67e0c0822c21d9e7\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424fca0debd1544e67e0c0822c21d9e7\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c6550fdb6c628b24ce9db4a741be8\transformed\lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c6550fdb6c628b24ce9db4a741be8\transformed\lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8b2f4de0ad0aa176dba9826c660dea7\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8b2f4de0ad0aa176dba9826c660dea7\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55d4dce1a1d094ee0ef77033f8ed8372\transformed\lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55d4dce1a1d094ee0ef77033f8ed8372\transformed\lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9a33d50cf3518ee712f7b48e2658100\transformed\core-ktx-1.9.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9a33d50cf3518ee712f7b48e2658100\transformed\core-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\jars\classes.jar"
      resolved="androidx.core:core:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30a67fdc3d1b95d3c56f9db8b33f6a04\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30a67fdc3d1b95d3c56f9db8b33f6a04\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f62e4a4d8a407c6b06d9e47fff0e003\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f62e4a4d8a407c6b06d9e47fff0e003\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"
      provided="true"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e490ace83d3e445f58e57bcd5ec1a3\transformed\lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e490ace83d3e445f58e57bcd5ec1a3\transformed\lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe9baeb1697de8666b4cdbdc3b7b7ec\transformed\lifecycle-livedata-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe9baeb1697de8666b4cdbdc3b7b7ec\transformed\lifecycle-livedata-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c6aba8d898bf5afa2792c4cbcbb6b8\transformed\lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c6aba8d898bf5afa2792c4cbcbb6b8\transformed\lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"
      provided="true"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18a53f08fca904f0165cea08f11ecb38\transformed\room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18a53f08fca904f0165cea08f11ecb38\transformed\room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:runner:1.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\455ebf020b8762ecd4720eee1633af45\transformed\runner-1.5.2\jars\classes.jar"
      resolved="androidx.test:runner:1.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\455ebf020b8762ecd4720eee1633af45\transformed\runner-1.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddbd4490448bb7f0c0a73a46ca27286c\transformed\storage-1.4.2\jars\classes.jar"
      resolved="androidx.test.services:storage:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddbd4490448bb7f0c0a73a46ca27286c\transformed\storage-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd49e1394edc9fb1e0c97db493156a5b\transformed\monitor-1.6.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd49e1394edc9fb1e0c97db493156a5b\transformed\monitor-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:annotation:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbf48f94d915492488c192018e373279\transformed\annotation-1.0.1\jars\classes.jar"
      resolved="androidx.test:annotation:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbf48f94d915492488c192018e373279\transformed\annotation-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\794c77caa217dc49438dbea4b18de558\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\794c77caa217dc49438dbea4b18de558\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185d612fe844270dbe94dbecbf717d3b\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185d612fe844270dbe94dbecbf717d3b\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667cc5877b26e9c23bb3ff4b31c492\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56667cc5877b26e9c23bb3ff4b31c492\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62ea7150938e602811be82533a7e3ed\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62ea7150938e602811be82533a7e3ed\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\661458eafcd301f339addf1cd11d3f67\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\661458eafcd301f339addf1cd11d3f67\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52aa3db48187e705030d064680ed98e4\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52aa3db48187e705030d064680ed98e4\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e79bbf8a7b26e1175d35315dd720102f\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e79bbf8a7b26e1175d35315dd720102f\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b36e363eec877c898c6a6b798c8febc9\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b36e363eec877c898c6a6b798c8febc9\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"
      provided="true"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"
      provided="true"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ce6023b12293a411384d5c2c100e77\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ce6023b12293a411384d5c2c100e77\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40b9f263e73af9f18fde6d0035bd27ff\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40b9f263e73af9f18fde6d0035bd27ff\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f71c86c6eabe71e98ff6abdb70ecc14\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f71c86c6eabe71e98ff6abdb70ecc14\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c376439633ee4e11e579bd0dba6d6e\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c376439633ee4e11e579bd0dba6d6e\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e0bcd8e0ccc9c7bfaf57677d7c3ed9\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e0bcd8e0ccc9c7bfaf57677d7c3ed9\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"
      provided="true"/>
  <library
      name="androidx.annotation:annotation:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation\1.5.0\857678d6b4ca7b28571ef7935c668bdb57e15027\annotation-1.5.0.jar"
      resolved="androidx.annotation:annotation:1.5.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="androidx.media3:media3-exoplayer:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcdce9d9bb74ce2c916a6bbd71ef776f\transformed\media3-exoplayer-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcdce9d9bb74ce2c916a6bbd71ef776f\transformed\media3-exoplayer-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-session:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1916aa273d557d541d17bc9f12ca7920\transformed\media3-session-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-session:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1916aa273d557d541d17bc9f12ca7920\transformed\media3-session-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb28fd5c9eb2b83999a9a874db3adbe5\transformed\media3-common-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-common:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb28fd5c9eb2b83999a9a874db3adbe5\transformed\media3-common-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9eef6ad673a264cc89263a38493d29db\transformed\annotation-experimental-1.3.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9eef6ad673a264cc89263a38493d29db\transformed\annotation-experimental-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.retrofit2:converter-scalars:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-scalars\2.9.0\e9ee6f35fb7ba877a35519f9a8f55c648df820d4\converter-scalars-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-scalars:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"
      provided="true"/>
  <library
      name="androidx.media3:media3-datasource-okhttp:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4caf9741b7f09b99a2a3a0bd3b3d7c6b\transformed\media3-datasource-okhttp-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource-okhttp:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4caf9741b7f09b99a2a3a0bd3b3d7c6b\transformed\media3-datasource-okhttp-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"
      provided="true"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.22"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce0ed41723d173c785d11a8e3f14149\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce0ed41723d173c785d11a8e3f14149\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"
      provided="true"/>
  <library
      name="androidx.media3:media3-ui:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78bf313ee09e26c8c671a0bbc2457ec1\transformed\media3-ui-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-ui:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78bf313ee09e26c8c671a0bbc2457ec1\transformed\media3-ui-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.multidex:multidex:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e0af7e6772353ecb8c2a0e492de2ce7\transformed\multidex-2.0.1\jars\classes.jar"
      resolved="androidx.multidex:multidex:2.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e0af7e6772353ecb8c2a0e492de2ce7\transformed\multidex-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="jp.wasabeef:glide-transformations:4.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80574656206597bee036fe941ba428c7\transformed\glide-transformations-4.3.0\jars\classes.jar"
      resolved="jp.wasabeef:glide-transformations:4.3.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80574656206597bee036fe941ba428c7\transformed\glide-transformations-4.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.zxing:core:3.4.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.zxing\core\3.4.1\1869da97e9b2b60b5ff2fcaf55899174b93ae25d\core-3.4.1.jar"
      resolved="com.google.zxing:core:3.4.1"
      provided="true"/>
  <library
      name="com.airbnb.android:lottie:6.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1736e1f51f6a8eb1a6b5aa43e9c24a34\transformed\lottie-6.1.0\jars\classes.jar"
      resolved="com.airbnb.android:lottie:6.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1736e1f51f6a8eb1a6b5aa43e9c24a34\transformed\lottie-6.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="de.hdodenhof:circleimageview:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d12885311d6e4a665c7a648b8563d6fa\transformed\circleimageview-3.1.0\jars\classes.jar"
      resolved="de.hdodenhof:circleimageview:3.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d12885311d6e4a665c7a648b8563d6fa\transformed\circleimageview-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-integration:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-integration\1.3\5de0c73fef18917cd85d0ab70bb23818685e4dfd\hamcrest-integration-1.3.jar"
      resolved="org.hamcrest:hamcrest-integration:1.3"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3fffdf45786e3c2b6c69e86201b2a80\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3fffdf45786e3c2b6c69e86201b2a80\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-core:2.50@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\hilt-core\2.50\476f7135e5fec8d1d80ad36a3cf5bf0074e1a503\hilt-core-2.50.jar"
      resolved="com.google.dagger:hilt-core:2.50"
      provided="true"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"
      provided="true"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b29013fc6fc9e803e176ae623a45241\transformed\espresso-idling-resource-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b29013fc6fc9e803e176ae623a45241\transformed\espresso-idling-resource-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup:javawriter:2.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup\javawriter\2.1.1\67ff45d9ae02e583d0f9b3432a5ebbe05c30c966\javawriter-2.1.1.jar"
      resolved="com.squareup:javawriter:2.1.1"/>
  <library
      name="com.google.dagger:dagger:2.50@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\dagger\2.50\78bcb2574731955ce15196c79c27b8c171a7af09\dagger-2.50.jar"
      resolved="com.google.dagger:dagger:2.50"
      provided="true"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"
      provided="true"/>
  <library
      name="androidx.media3:media3-container:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\195bf0261f5accce3675bff17e890464\transformed\media3-container-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-container:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\195bf0261f5accce3675bff17e890464\transformed\media3-container-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c21fd9d5c777f873383f9d1179d8ce8\transformed\media3-datasource-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c21fd9d5c777f873383f9d1179d8ce8\transformed\media3-datasource-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b148c17468b063643693b5fb41c2c086\transformed\media3-decoder-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-decoder:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b148c17468b063643693b5fb41c2c086\transformed\media3-decoder-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb7207166b230fc420f90518c02881cc\transformed\media3-extractor-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-extractor:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb7207166b230fc420f90518c02881cc\transformed\media3-extractor-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a26edc275529aa4f7cf05aefcad03d8e\transformed\media3-database-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-database:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a26edc275529aa4f7cf05aefcad03d8e\transformed\media3-database-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"
      provided="true"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"
      provided="true"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2b4430280827f7182af831eee6980fb\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2b4430280827f7182af831eee6980fb\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:dagger-lint-aar:2.50@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff1bdd17c51c51bdc66035dbacd7b9c\transformed\dagger-lint-aar-2.50\jars\classes.jar"
      resolved="com.google.dagger:dagger-lint-aar:2.50"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff1bdd17c51c51bdc66035dbacd7b9c\transformed\dagger-lint-aar-2.50"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"
      provided="true"/>
</libraries>
