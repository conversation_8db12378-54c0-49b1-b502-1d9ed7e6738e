1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.aimusicplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 百度语音SDK所需权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:5-66
12-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:5-78
13-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:5-75
14-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
15-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:9:22-77
16
17    <!-- Android 13 (API 33) 及以上版本需要区分的存储权限 -->
18    <uses-permission
18-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission
21-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:5-15:38
22        android:name="android.permission.READ_EXTERNAL_STORAGE"
22-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:14:22-77
23        android:maxSdkVersion="32" />
23-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:15:9-35
24
25    <!-- Android 13 新增的精细存储权限 -->
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:5-75
26-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:18:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:5-76
27-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:19:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
28-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:5-75
28-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:20:22-72
29
30    <!-- 应用所需的其他权限 -->
31    <uses-permission android:name="android.permission.RECORD_AUDIO" />
31-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:5-70
31-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:23:22-68
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
32-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:5-78
32-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:24:22-76
33    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
33-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:5-74
33-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:25:22-72
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:5-78
34-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:26:22-75
35
36    <!-- 通知权限 (Android 13+) -->
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:5-77
37-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:29:22-74
38
39    <!-- 前台服务权限 -->
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
40-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:5-77
40-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:32:22-74
41
42    <!-- 震动权限 -->
43    <uses-permission android:name="android.permission.VIBRATE" />
43-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:5-66
43-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:35:22-63
44    <uses-permission android:name="android.permission.CAMERA" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
44-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:5-65
44-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:22:22-62
45    <uses-feature
45-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:25:5-27:36
46        android:name="android.hardware.camera"
46-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:26:9-47
47        android:required="false" />
47-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:27:9-33
48    <uses-feature
48-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:28:5-30:36
49        android:name="android.hardware.camera.front"
49-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:29:9-53
50        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
50-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:30:9-33
51    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
52    <uses-feature
52-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:33:5-35:36
53        android:name="android.hardware.camera.autofocus"
53-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:34:9-57
54        android:required="false" />
54-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:35:9-33
55    <uses-feature
55-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:36:5-38:36
56        android:name="android.hardware.camera.flash"
56-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:37:9-53
57        android:required="false" />
57-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:38:9-33
58    <uses-feature
58-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:39:5-41:36
59        android:name="android.hardware.screen.landscape"
59-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:40:9-57
60        android:required="false" />
60-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:41:9-33
61    <uses-feature
61-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:42:5-44:36
62        android:name="android.hardware.wifi"
62-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:43:9-45
63        android:required="false" />
63-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:44:9-33
64
65    <permission
65-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
66        android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
66-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
67        android:protectionLevel="signature" />
67-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
68
69    <uses-permission android:name="com.example.aimusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
69-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
69-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
70
71    <application
71-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:37:5-106:19
72        android:name="com.example.aimusicplayer.MusicApplication"
72-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:38:9-41
73        android:allowBackup="true"
73-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:39:9-35
74        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
74-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48fbfb4201531ba0d2c54a69b6a94add\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
75        android:dataExtractionRules="@xml/data_extraction_rules"
75-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:40:9-65
76        android:debuggable="true"
77        android:extractNativeLibs="false"
78        android:fullBackupContent="@xml/backup_rules"
78-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:41:9-54
79        android:icon="@mipmap/ic_launcher"
79-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:42:9-43
80        android:label="轻聆"
80-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:43:9-27
81        android:networkSecurityConfig="@xml/network_security_config"
81-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:48:9-69
82        android:requestLegacyExternalStorage="true"
82-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:46:9-52
83        android:supportsRtl="true"
83-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:44:9-35
84        android:theme="@style/Theme.AIMusicPlayer"
84-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:45:9-51
85        android:usesCleartextTraffic="true" >
85-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:47:9-44
86
87        <!-- 百度语音SDK必要的meta-data配置 -->
88        <meta-data
88-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:52:9-54:41
89            android:name="com.baidu.speech.APP_ID"
89-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:53:13-51
90            android:value="118558442" />
90-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:54:13-38
91        <meta-data
91-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:55:9-57:56
92            android:name="com.baidu.speech.API_KEY"
92-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:56:13-52
93            android:value="l07tTLiM8XdSVcM6Avmv5FG3" />
93-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:57:13-53
94        <meta-data
94-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:58:9-60:64
95            android:name="com.baidu.speech.SECRET_KEY"
95-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:59:13-55
96            android:value="e4DxN5gewACp162txczyVRuJs4UGBhdb" />
96-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:60:13-61
97
98        <!-- 启动页 -->
99        <activity
99-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:63:9-72:20
100            android:name="com.example.aimusicplayer.ui.splash.SplashActivity"
100-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:64:13-53
101            android:exported="true"
101-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:65:13-36
102            android:screenOrientation="landscape"
102-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:66:13-50
103            android:theme="@style/FullScreenTheme" >
103-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:67:13-51
104            <intent-filter>
104-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:68:13-71:29
105                <action android:name="android.intent.action.MAIN" />
105-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:69:17-69
105-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:69:25-66
106
107                <category android:name="android.intent.category.LAUNCHER" />
107-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:70:17-77
107-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:70:27-74
108            </intent-filter>
109        </activity>
110
111        <!-- 登录页 -->
112        <activity
112-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:75:9-79:54
113            android:name="com.example.aimusicplayer.ui.login.LoginActivity"
113-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:76:13-51
114            android:exported="false"
114-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:77:13-37
115            android:screenOrientation="landscape"
115-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:78:13-50
116            android:theme="@style/FullScreenTheme" />
116-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:79:13-51
117
118        <!-- 主界面（已重命名，移除了MainActivity2） -->
119        <activity
119-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:82:9-86:54
120            android:name="com.example.aimusicplayer.ui.main.MainActivity"
120-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:83:13-49
121            android:exported="false"
121-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:84:13-37
122            android:screenOrientation="landscape"
122-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:85:13-50
123            android:theme="@style/FullScreenTheme" />
123-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:86:13-51
124
125        <!-- 播放服务 -->
126        <service
126-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:89:9-92:40
127            android:name="com.example.aimusicplayer.service.UnifiedPlaybackService"
127-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:90:13-59
128            android:enabled="true"
128-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:91:13-35
129            android:exported="false" />
129-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:92:13-37
130
131        <!-- 播放器页面已迁移到Fragment，不再需要单独的Activity -->
132        <!-- PlayerActivity已删除，功能已迁移到PlayerFragment -->
133
134        <provider
135            android:name="androidx.core.content.FileProvider"
135-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:98:13-62
136            android:authorities="com.example.aimusicplayer.provider"
136-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:99:13-60
137            android:exported="false"
137-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:100:13-37
138            android:grantUriPermissions="true" >
138-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:101:13-47
139            <meta-data
139-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:102:13-104:54
140                android:name="android.support.FILE_PROVIDER_PATHS"
140-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:103:17-67
141                android:resource="@xml/file_paths" />
141-->C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\AndroidManifest.xml:104:17-51
142        </provider>
143
144        <activity
144-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
145            android:name="com.karumi.dexter.DexterActivity"
145-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
146            android:theme="@style/Dexter.Internal.Theme.Transparent" />
146-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b63a40a644c4ff2b899324bd1d1a02\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
147        <activity
147-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:47:9-53:63
148            android:name="com.journeyapps.barcodescanner.CaptureActivity"
148-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:48:13-74
149            android:clearTaskOnLaunch="true"
149-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:49:13-45
150            android:screenOrientation="sensorLandscape"
150-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:50:13-56
151            android:stateNotNeeded="true"
151-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:51:13-42
152            android:theme="@style/zxing_CaptureTheme"
152-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:52:13-54
153            android:windowSoftInputMode="stateAlwaysHidden" />
153-->[com.journeyapps:zxing-android-embedded:4.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8cb4a267669986cd76225679b8b78b\transformed\zxing-android-embedded-4.2.0\AndroidManifest.xml:53:13-60
154
155        <meta-data
155-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:10:9-12:43
156            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
156-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:11:13-84
157            android:value="GlideModule" />
157-->[com.github.bumptech.glide:okhttp3-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2253a27b5dc759780db3c74df291e5b\transformed\okhttp3-integration-4.16.0\AndroidManifest.xml:12:13-40
158
159        <provider
159-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
160            android:name="androidx.startup.InitializationProvider"
160-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
161            android:authorities="com.example.aimusicplayer.androidx-startup"
161-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
162            android:exported="false" >
162-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
163            <meta-data
163-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
164                android:name="androidx.emoji2.text.EmojiCompatInitializer"
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
165                android:value="androidx.startup" />
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee23484e15125fd8d0a8afb0f0603a9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
166            <meta-data
166-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
167                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
167-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
168                android:value="androidx.startup" />
168-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3561efb20ac5d6136e1c3393e8a6bbc0\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
169            <meta-data
169-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
170-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
171                android:value="androidx.startup" />
171-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
172        </provider>
173
174        <uses-library
174-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
175            android:name="androidx.window.extensions"
175-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
176            android:required="false" />
176-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
177        <uses-library
177-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
178            android:name="androidx.window.sidecar"
178-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
179            android:required="false" />
179-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b358dfc31cecf53833e2b1f5ca9c9c\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
180
181        <service
181-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
182            android:name="androidx.room.MultiInstanceInvalidationService"
182-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
183            android:directBootAware="true"
183-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
184            android:exported="false" />
184-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34d010526148f493071b1b4f5da875cc\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
185
186        <receiver
186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
187            android:name="androidx.profileinstaller.ProfileInstallReceiver"
187-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
188            android:directBootAware="false"
188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
189            android:enabled="true"
189-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
190            android:exported="true"
190-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
191            android:permission="android.permission.DUMP" >
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
192            <intent-filter>
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
193                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
194            </intent-filter>
195            <intent-filter>
195-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
196                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
197            </intent-filter>
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
199                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
200            </intent-filter>
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
202                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae706378acc0de26a672fefdc1892496\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
203            </intent-filter>
204        </receiver>
205    </application>
206
207</manifest>
