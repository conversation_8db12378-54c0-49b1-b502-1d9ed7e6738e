package com.example.aimusicplayer.utils;

import androidx.recyclerview.widget.DiffUtil;
import com.example.aimusicplayer.model.TopListResponse;
import java.util.List;

/**
 * Java版本的DiffCallbacks工具类
 * 为Java代码提供DiffUtil回调
 */
public class JavaDiffCallbacks {

    /**
     * 播放列表差异比较回调（TopListResponse.PlayList版本）
     * 用于优化播放列表的更新，减少不必要的重绘
     */
    public static class PlaylistDiffCallback extends DiffUtil.Callback {
        private final List<TopListResponse.PlayList> oldPlaylists;
        private final List<TopListResponse.PlayList> newPlaylists;

        public PlaylistDiffCallback(List<TopListResponse.PlayList> oldPlaylists,
                                  List<TopListResponse.PlayList> newPlaylists) {
            this.oldPlaylists = oldPlaylists;
            this.newPlaylists = newPlaylists;
        }

        @Override
        public int getOldListSize() {
            return oldPlaylists.size();
        }

        @Override
        public int getNewListSize() {
            return newPlaylists.size();
        }

        @Override
        public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
            // 比较播放列表ID是否相同
            return oldPlaylists.get(oldItemPosition).getId() ==
                   newPlaylists.get(newItemPosition).getId();
        }

        @Override
        public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
            // 比较播放列表内容是否相同
            TopListResponse.PlayList oldPlaylist = oldPlaylists.get(oldItemPosition);
            TopListResponse.PlayList newPlaylist = newPlaylists.get(newItemPosition);

            boolean nameSame = (oldPlaylist.getName() != null && oldPlaylist.getName().equals(newPlaylist.getName())) ||
                              (oldPlaylist.getName() == null && newPlaylist.getName() == null);
            boolean coverSame = (oldPlaylist.getCoverImgUrl() != null && oldPlaylist.getCoverImgUrl().equals(newPlaylist.getCoverImgUrl())) ||
                               (oldPlaylist.getCoverImgUrl() == null && newPlaylist.getCoverImgUrl() == null);
            boolean playcountSame = oldPlaylist.getPlayCount() == newPlaylist.getPlayCount();

            return nameSame && coverSame && playcountSame;
        }
    }
}
