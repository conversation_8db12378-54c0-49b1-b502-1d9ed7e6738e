package com.example.aimusicplayer.api;

import com.example.aimusicplayer.model.AlbumResponse;
import com.example.aimusicplayer.data.model.BannerResponse;
import com.example.aimusicplayer.model.HotSearchResponse;
import com.example.aimusicplayer.model.LikeListResponse;
import com.example.aimusicplayer.model.LikeResponse;
import com.example.aimusicplayer.data.model.LyricResponse;
import com.example.aimusicplayer.model.MusicAvailabilityResponse;
import com.example.aimusicplayer.model.PersonalizedSongResponse;
import com.example.aimusicplayer.model.PlaylistDetailResponse;
import com.example.aimusicplayer.model.QrCheckResponse;
import com.example.aimusicplayer.model.QrCodeResponse;
import com.example.aimusicplayer.model.QrKeyResponse;
import com.example.aimusicplayer.model.RecommendPlaylistResponse;
import com.example.aimusicplayer.model.RecommendSongsResponse;
import com.example.aimusicplayer.model.SearchResponse;
import com.example.aimusicplayer.model.SearchSuggestResponse;
import com.example.aimusicplayer.data.model.SongDetailResponse;
import com.example.aimusicplayer.model.SongUrlResponse;
import com.example.aimusicplayer.model.TopListResponse;
import com.example.aimusicplayer.data.model.UserDetailResponse;
import com.example.aimusicplayer.model.UserPlaylistResponse;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Header;

import retrofit2.http.Query;

/**
 * 统一的网易云音乐API服务接口
 * 基于api.txt中的接口定义
 * 使用MVVM架构
 */
public interface UnifiedApiService {

    /**
     * 检查歌曲是否可用
     * @param id 歌曲ID
     * @return 歌曲可用性响应
     */
    @GET("check/music")
    Call<MusicAvailabilityResponse> checkMusicAvailability(@Query("id") String id);

    /**
     * 获取歌曲详情
     * @param ids 歌曲ID，多个用逗号分隔
     * @return 歌曲详情响应
     */
    @GET("song/detail")
    Call<SongDetailResponse> getSongDetail(@Query("ids") String ids);

    /**
     * 获取歌曲URL
     * @param id 歌曲ID
     * @return 歌曲URL响应
     */
    @GET("song/url")
    Call<SongUrlResponse> getSongUrl(@Query("id") String id);

    /**
     * 获取歌曲URL (V1版本)
     * @param id 歌曲ID
     * @param level 音质等级 (standard, higher, exhigh, lossless, hires)
     * @return 歌曲URL响应
     */
    @GET("song/url/v1")
    Call<SongUrlResponse> getSongUrlV1(@Query("id") String id, @Query("level") String level);

    /**
     * 收藏/取消收藏歌曲
     * @param id 歌曲ID
     * @param like true表示收藏，false表示取消收藏
     * @param timestamp 时间戳
     * @return 响应
     */
    @GET("like")
    Call<LikeResponse> likeSong(@Query("id") String id, @Query("like") boolean like, @Query("timestamp") long timestamp);

    /**
     * 获取用户收藏的歌曲ID列表
     * @param uid 用户ID
     * @return 收藏歌曲ID列表响应
     */
    @GET("likelist")
    Call<LikeListResponse> getLikeList(@Query("uid") String uid);

    /**
     * 获取歌词
     * @param id 歌曲ID
     * @return 歌词响应
     */
    @GET("lyric")
    Call<LyricResponse> getLyric(@Query("id") String id);

    /**
     * 获取专辑信息
     * @param id 专辑ID
     * @return 专辑响应
     */
    @GET("album")
    Call<AlbumResponse> getAlbum(@Query("id") String id);

    /**
     * 获取每日推荐歌曲
     * @return 推荐歌曲响应
     */
    @GET("recommend/songs")
    Call<RecommendSongsResponse> getRecommendSongs();

    /**
     * 发送手机验证码
     * @param phone 手机号码
     * @return 响应结果
     */
    @GET("captcha/sent")
    Call<ResponseBody> sendCaptcha(@Query("phone") String phone);

    /**
     * 验证验证码是否正确
     * @param phone 手机号
     * @param captcha 验证码
     * @param ctcode 国家区号，默认86即中国
     * @return 验证结果
     */
    @GET("captcha/verify")
    Call<ResponseBody> verifyCaptcha(@Query("phone") String phone, @Query("captcha") String captcha, @Query("ctcode") String ctcode);

    /**
     * 验证验证码是否正确（使用默认国家区号）
     * @param phone 手机号
     * @param captcha 验证码
     * @return 验证结果
     */
    @GET("captcha/verify")
    Call<ResponseBody> verifyCaptcha(@Query("phone") String phone, @Query("captcha") String captcha);

    /**
     * 使用验证码进行登录
     * @param phone 手机号码
     * @param captcha 验证码
     * @return 登录响应结果
     */
    @GET("login/cellphone")
    Call<ResponseBody> loginWithCaptcha(@Query("phone") String phone, @Query("captcha") String captcha);

    /**
     * 使用密码进行登录
     * @param phone 手机号码
     * @param password 密码
     * @return 登录响应结果
     */
    @GET("login/cellphone")
    Call<ResponseBody> loginWithPassword(@Query("phone") String phone, @Query("password") String password);

    /**
     * 获取用户账号信息
     * @param cookie 可选，用户登录Cookie
     * @return 用户信息响应结果
     */
    @GET("user/account")
    Call<ResponseBody> getUserAccount(@Header("Cookie") String cookie);

    /**
     * 游客登录
     * @param timestamp 时间戳，防止缓存
     * @return 登录响应结果
     */
    @GET("register/anonimous")
    Call<ResponseBody> guestLogin(@Query("timestamp") long timestamp);

    /**
     * 获取二维码key
     * @param timestamp 时间戳，防止缓存
     * @return 二维码key响应
     */
    @GET("login/qr/key")
    Call<QrKeyResponse> getQrKey(@Query("timestamp") long timestamp);

    /**
     * 获取二维码
     * @param key 二维码key
     * @param qrimg 是否返回二维码图片，1为返回，0为不返回
     * @param timestamp 时间戳，防止缓存
     * @return 二维码响应
     */
    @GET("login/qr/create")
    Call<QrCodeResponse> getQrCode(@Query("key") String key, @Query("qrimg") int qrimg, @Query("timestamp") long timestamp);

    /**
     * 获取二维码图片
     * @param key 二维码key
     * @param timestamp 时间戳，防止缓存
     * @return 二维码响应
     */
    @GET("login/qr/create")
    Call<QrCodeResponse> getQrImage(@Query("key") String key, @Query("timestamp") long timestamp);

    /**
     * 检查二维码状态
     * @param key 二维码key
     * @param timestamp 时间戳，防止缓存
     * @return 二维码状态响应
     */
    @GET("login/qr/check")
    Call<QrCheckResponse> checkQrStatus(@Query("key") String key, @Query("timestamp") long timestamp);

    /**
     * 检查二维码状态（带noCookie参数）
     * @param key 二维码key
     * @param timestamp 时间戳，防止缓存
     * @param noCookie 是否不需要cookie，true表示不需要
     * @return 二维码状态响应
     */
    @GET("login/qr/check")
    Call<QrCheckResponse> checkQrStatusNoCookie(@Query("key") String key, @Query("timestamp") long timestamp, @Query("noCookie") boolean noCookie);

    /**
     * 获取用户详情
     * @param uid 用户ID
     * @return 用户详情响应
     */
    @GET("user/detail")
    Call<UserDetailResponse> getUserDetail(@Query("uid") String uid);

    /**
     * 获取用户歌单
     * @param uid 用户ID
     * @param limit 返回数量限制
     * @return 用户歌单响应
     */
    @GET("user/playlist")
    Call<UserPlaylistResponse> getUserPlaylist(@Query("uid") String uid, @Query("limit") int limit);

    /**
     * 获取用户歌单（默认不限制数量）
     * @param uid 用户ID
     * @return 用户歌单响应
     */
    @GET("user/playlist")
    Call<UserPlaylistResponse> getUserPlaylist(@Query("uid") String uid);

    /**
     * 获取歌单详情
     * @param id 歌单ID
     * @return 歌单详情响应
     */
    @GET("playlist/detail")
    Call<PlaylistDetailResponse> getPlaylistDetail(@Query("id") String id);

    /**
     * 获取推荐歌单
     * @return 推荐歌单响应
     */
    @GET("personalized")
    Call<RecommendPlaylistResponse> getRecommendPlaylists();

    /**
     * 添加歌曲到歌单
     * @param op 操作类型，add为添加
     * @param pid 歌单ID
     * @param tracks 歌曲ID，多个用逗号分隔
     * @return 响应结果
     */
    @GET("playlist/tracks")
    Call<ResponseBody> addToPlaylist(@Query("op") String op, @Query("pid") String pid, @Query("tracks") String tracks);

    /**
     * 获取心动模式推荐歌曲
     * @param id 当前歌曲ID
     * @param pid 当前歌单ID
     * @return 心动模式推荐歌曲响应
     */
    @GET("playmode/intelligence/list")
    Call<ResponseBody> getIntelligenceList(@Query("id") String id, @Query("pid") String pid);

    /**
     * 获取相似歌曲
     * @param id 歌曲ID
     * @param limit 返回数量
     * @param offset 偏移数量，用于分页
     * @return 相似歌曲响应
     */
    @GET("simi/song")
    Call<ResponseBody> getSimilarSongs(@Query("id") String id, @Query("limit") int limit, @Query("offset") int offset);

    /**
     * 获取相似歌曲（默认参数）
     * @param id 歌曲ID
     * @return 相似歌曲响应
     */
    @GET("simi/song")
    Call<ResponseBody> getSimilarSongs(@Query("id") String id);

    /**
     * 获取推荐新音乐
     * @param limit 返回数量
     * @return 推荐新音乐响应
     */
    @GET("personalized/newsong")
    Call<PersonalizedSongResponse> getNewSongs(@Query("limit") int limit);

    /**
     * 获取所有排行榜
     * @return 排行榜响应
     */
    @GET("toplist")
    Call<TopListResponse> getTopLists();

    /**
     * 搜索
     * @param keywords 关键词
     * @param type 搜索类型，1: 单曲, 10: 专辑, 100: 歌手, 1000: 歌单, 1002: 用户, 1004: MV, 1006: 歌词, 1009: 电台, 1014: 视频
     * @param limit 返回数量
     * @param offset 偏移数量，用于分页
     * @return 搜索响应
     */
    @GET("search")
    Call<SearchResponse> search(@Query("keywords") String keywords, @Query("type") int type, @Query("limit") int limit, @Query("offset") int offset);

    /**
     * 默认搜索（单曲）
     * @param keywords 关键词
     * @return 搜索响应
     */
    @GET("search")
    Call<SearchResponse> searchMusic(@Query("keywords") String keywords);

    /**
     * 获取热搜列表
     * @return 热搜列表响应
     */
    @GET("search/hot/detail")
    Call<HotSearchResponse> getHotSearchList();

    /**
     * 获取搜索建议
     * @param keywords 关键词
     * @return 搜索建议响应
     */
    @GET("search/suggest")
    Call<SearchSuggestResponse> getSearchSuggest(@Query("keywords") String keywords, @Query("type") String type);

    /**
     * 获取轮播图
     * @param type 资源类型，0: PC, 1: Android, 2: iPhone, 3: iPad
     * @return 轮播图响应
     */
    @GET("banner")
    Call<BannerResponse> getBanner(@Query("type") int type);

    /**
     * 获取登录状态
     * @param cookie 可选，用户登录Cookie
     * @return 登录状态响应结果
     */
    @GET("login/status")
    Call<ResponseBody> getLoginStatus(@Header("Cookie") String cookie);

    /**
     * 退出登录
     * @return 响应结果
     */
    @GET("logout")
    Call<ResponseBody> logout();

    /**
     * 获取歌曲评论
     * @param id 歌曲ID
     * @param limit 返回数量
     * @param offset 偏移数量，用于分页
     * @return 评论响应
     */
    @GET("comment/music")
    Call<ResponseBody> getMusicComments(@Query("id") String id, @Query("limit") int limit, @Query("offset") int offset);

    /**
     * 获取歌曲评论（默认参数）
     * @param id 歌曲ID
     * @return 评论响应
     */
    @GET("comment/music")
    Call<ResponseBody> getMusicComments(@Query("id") String id);

    /**
     * 发送评论
     * @param t 评论类型，0: 歌曲, 1: mv, 2: 歌单, 3: 专辑, 4: 电台, 5: 视频, 6: 动态
     * @param id 对应资源ID
     * @param content 评论内容
     * @return 评论响应
     */
    @GET("comment")
    Call<ResponseBody> sendComment(@Query("t") int t, @Query("id") String id, @Query("content") String content);

    /**
     * 给评论点赞
     * @param id 资源ID
     * @param cid 评论ID
     * @param t 是否点赞，1: 点赞, 0: 取消点赞
     * @param type 资源类型，0: 歌曲, 1: mv, 2: 歌单, 3: 专辑, 4: 电台, 5: 视频, 6: 动态
     * @return 点赞响应
     */
    @GET("comment/like")
    Call<ResponseBody> likeComment(@Query("id") String id, @Query("cid") String cid, @Query("t") int t, @Query("type") int type);
}
