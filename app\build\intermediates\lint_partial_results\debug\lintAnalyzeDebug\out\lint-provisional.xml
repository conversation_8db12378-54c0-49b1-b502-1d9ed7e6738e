<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="conditional_incidents">

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt"
            line="163"
            column="13"
            startOffset="5371"
            endLine="163"
            endColumn="82"
            endOffset="5440"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt"
            line="167"
            column="13"
            startOffset="5598"
            endLine="167"
            endColumn="40"
            endOffset="5625"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/CommentAdapter.kt"
            line="147"
            column="21"
            startOffset="5002"
            endLine="147"
            endColumn="107"
            endOffset="5088"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/CommentAdapter.kt"
            line="170"
            column="21"
            startOffset="5931"
            endLine="170"
            endColumn="107"
            endOffset="6017"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/CommentAdapter.kt"
            line="198"
            column="21"
            startOffset="7054"
            endLine="198"
            endColumn="107"
            endOffset="7140"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/comment/CommentFragment.kt"
            line="214"
            column="17"
            startOffset="7051"
            endLine="214"
            endColumn="103"
            endOffset="7137"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/player/PlayerFragment.kt"
            line="285"
            column="13"
            startOffset="8979"
            endLine="285"
            endColumn="121"
            endOffset="9087"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/ReplyAdapter.kt"
            line="103"
            column="21"
            startOffset="3273"
            endLine="103"
            endColumn="107"
            endOffset="3359"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.VIBRATE" message="Missing permissions required by Vibrator.vibrate: %1$s" lastApi="**********"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/adapter/ReplyAdapter.kt"
            line="151"
            column="21"
            startOffset="5238"
            endLine="151"
            endColumn="107"
            endOffset="5324"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Vibrator.vibrate: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.VIBRATE"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="12"
            column="36"
            startOffset="568"
            endLine="12"
            endColumn="77"
            endOffset="609"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="32"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="14"
            column="36"
            startOffset="684"
            endLine="14"
            endColumn="76"
            endOffset="724"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="32"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="fffffffffe000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/ButtonAnimationUtils.kt"
            line="159"
            column="26"
            startOffset="5164"
            endLine="159"
            endColumn="59"
            endOffset="5197"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 26 (current min is %1$s): `android.os.VibrationEffect#DEFAULT_AMPLITUDE`"/>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="DEFAULT_AMPLITUDE"/>
            <entry
                name="owner"
                string="android.os.VibrationEffect"/>
            <api-levels id="requiresApi"
                value="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="ffffffff00000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/main/MainActivity.java"
            line="81"
            column="9"
            startOffset="2377"
            endLine="81"
            endColumn="47"
            endOffset="2415"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 33 (current min is %1$s): `android.Manifest.permission#POST_NOTIFICATIONS`"/>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="POST_NOTIFICATIONS"/>
            <entry
                name="owner"
                string="android.Manifest.permission"/>
            <api-levels id="requiresApi"
                value="ffffffff00000000"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="ffffffffc0000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/service/UnifiedPlaybackService.kt"
            line="70"
            column="72"
            startOffset="2605"
            endLine="70"
            endColumn="83"
            endOffset="2616"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 31 (current min is %1$s): `java.lang.Class#getPackageName`"/>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="getPackageName"/>
            <entry
                name="owner"
                string="java.lang.Class"/>
            <api-levels id="requiresApi"
                value="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/service/UnifiedPlaybackService.kt"
            line="628"
            column="9"
            startOffset="20567"
            endLine="628"
            endColumn="84"
            endOffset="20642"/>
    </incident>

    <incident
        id="CustomSplashScreen"
        severity="warning"
        message="The application should not provide its own launch screen">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/ui/splash/SplashActivity.java"
            line="32"
            column="14"
            startOffset="908"
            endLine="32"
            endColumn="28"
            endOffset="922"/>
        <map>
            <condition targetGE="31"/>
        </map>
    </incident>

    <incident
        id="ForegroundServiceType"
        severity="error"
        message="To call `Service.startForeground()`, the `&lt;service>` element of manifest file must have the `foregroundServiceType` attribute specified">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/service/UnifiedPlaybackService.kt"
            line="227"
            column="9"
            startOffset="7608"
            endLine="227"
            endColumn="24"
            endOffset="7623"/>
        <map>
            <condition targetGE="34"/>
        </map>
    </incident>

    <incident
        id="UnspecifiedRegisterReceiverFlag"
        severity="error"
        message="`playbackControlReceiver` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for com.example.aimusicplayer.ACTION_PLAY, com.example.aimusicplayer.ACTION_PAUSE, com.example.aimusicplayer.ACTION_PREVIOUS, com.example.aimusicplayer.ACTION_NEXT, com.example.aimusicplayer.ACTION_STOP">
        <fix-data/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/service/UnifiedPlaybackService.kt"
            line="222"
            column="9"
            startOffset="7443"
            endLine="222"
            endColumn="58"
            endOffset="7492"/>
        <map>
            <entry
                name="hasUnprotected"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="43"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml"
            line="8"
            column="5"
            startOffset="268"
            endLine="8"
            endColumn="25"
            endOffset="288"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="This attribute is not supported in images generated from this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml"
            line="10"
            column="28"
            startOffset="334"
            endLine="10"
            endColumn="48"
            endOffset="354"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_more_vert.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="43"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_more_vert.xml"
            line="9"
            column="28"
            startOffset="302"
            endLine="9"
            endColumn="48"
            endOffset="322"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml"
            line="7"
            column="28"
            startOffset="221"
            endLine="7"
            endColumn="48"
            endOffset="241"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="43"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh.xml"
            line="9"
            column="28"
            startOffset="302"
            endLine="9"
            endColumn="48"
            endOffset="322"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_share.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="43"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_share.xml"
            line="9"
            column="28"
            startOffset="302"
            endLine="9"
            endColumn="48"
            endOffset="322"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 33">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="2099"
                endOffset="2119"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="46"
            column="9"
            startOffset="2099"
            endLine="46"
            endColumn="29"
            endOffset="2119"/>
        <map>
            <condition minGE="ffffffff00000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/BlurUtils.kt"
                startOffset="3505"
                endOffset="3540"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/aimusicplayer/utils/BlurUtils.kt"
            line="119"
            column="5"
            startOffset="3505"
            endLine="119"
            endColumn="40"
            endOffset="3540"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
                startOffset="713"
                endOffset="733"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="13"
            column="45"
            startOffset="713"
            endLine="13"
            endColumn="65"
            endOffset="733"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
                startOffset="713"
                endOffset="733"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="13"
            column="45"
            startOffset="713"
            endLine="13"
            endColumn="65"
            endOffset="733"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentLeft` with `android:layout_alignParentStart=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentStart=&quot;true&quot;"
            oldString="layout_alignParentLeft"
            replacement="layout_alignParentStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="214"
            column="9"
            startOffset="7901"
            endLine="214"
            endColumn="39"
            endOffset="7931"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;16dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;16dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="216"
            column="9"
            startOffset="7987"
            endLine="216"
            endColumn="34"
            endOffset="8012"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

</incidents>
