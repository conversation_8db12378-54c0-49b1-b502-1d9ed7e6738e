package com.example.aimusicplayer.utils

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onStart
import retrofit2.HttpException
import retrofit2.Response
import java.io.IOException

/**
 * 网络请求结果封装类
 * 用于表示网络请求的不同状态：加载中、成功、错误
 */
sealed class NetworkResult<out T> {
    /**
     * 加载中状态
     */
    object Loading : NetworkResult<Nothing>()

    /**
     * 成功状态
     * @param data 请求成功返回的数据
     */
    data class Success<T>(val data: T) : NetworkResult<T>()

    /**
     * 错误状态
     * @param message 错误信息
     * @param code 错误码，默认为-1
     */
    data class Error<T>(val message: String, val code: Int = -1) : NetworkResult<T>()

    /**
     * 判断是否为成功状态
     */
    val isSuccess: Boolean
        get() = this is Success

    /**
     * 判断是否为加载中状态
     */
    val isLoading: Boolean
        get() = this is Loading

    /**
     * 判断是否为错误状态
     */
    val isError: Boolean
        get() = this is Error

    /**
     * 获取数据，如果不是成功状态则返回null
     */
    fun getOrNull(): T? = if (this is Success) data else null

    /**
     * 获取错误信息，如果不是错误状态则返回null
     */
    fun errorOrNull(): String? = if (this is Error) message else null

    /**
     * 映射数据转换
     * @param transform 数据转换函数
     */
    fun <R> map(transform: (T) -> R): NetworkResult<R> {
        return when (this) {
            is Loading -> Loading
            is Error -> Error<R>(message, code)
            is Success -> Success(transform(data))
        }
    }

    /**
     * 处理不同状态
     * @param onLoading 加载中状态处理函数
     * @param onSuccess 成功状态处理函数
     * @param onError 错误状态处理函数
     */
    fun handle(
        onLoading: () -> Unit = {},
        onSuccess: (T) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        when (this) {
            is Loading -> onLoading()
            is Success -> onSuccess(data)
            is Error -> onError(message)
        }
    }

    companion object {
        /**
         * 从异常创建错误结果
         * @param throwable 异常
         */
        fun <T> error(throwable: Throwable): Error<T> {
            val code = when (throwable) {
                is HttpException -> throwable.code()
                else -> -1
            }
            return Error(throwable.message ?: "未知错误", code)
        }

        /**
         * 从可空数据创建结果
         * @param data 数据
         * @param errorMessage 数据为空时的错误信息
         */
        fun <T> fromNullable(data: T?, errorMessage: String = "数据为空"): NetworkResult<T> {
            return if (data != null) {
                Success(data)
            } else {
                Error<T>(errorMessage)
            }
        }

        /**
         * 创建网络请求Flow
         * @param call 挂起函数，执行网络请求
         * @param onStart 开始请求时的回调
         * @return Flow<NetworkResult<T>>
         */
        fun <T> apiFlow(
            call: suspend () -> T,
            onStart: () -> Unit = {}
        ): Flow<NetworkResult<T>> = flow {
            emit(Loading)
            val result = call()
            emit(Success(result))
        }.onStart {
            onStart()
        }.catch { e ->
            emit(error(e))
        }

        /**
         * 创建Retrofit响应Flow
         * @param call 挂起函数，执行Retrofit请求
         * @param onStart 开始请求时的回调
         * @return Flow<NetworkResult<T>>
         */
        fun <T> apiResponseFlow(
            call: suspend () -> Response<T>,
            onStart: () -> Unit = {}
        ): Flow<NetworkResult<T>> = flow {
            emit(Loading)
            val response = call()
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    emit(Success(body))
                } else {
                    emit(Error<T>("响应成功但数据为空"))
                }
            } else {
                emit(Error<T>("请求失败: ${response.code()} ${response.message()}", response.code()))
            }
        }.onStart {
            onStart()
        }.catch { e ->
            emit(error(e))
        }
    }
}
