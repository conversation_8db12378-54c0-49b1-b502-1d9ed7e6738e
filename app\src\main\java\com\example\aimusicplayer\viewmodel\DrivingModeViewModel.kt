package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.content.Context
import android.media.AudioManager
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.data.repository.SettingsRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.utils.NetworkResult
import com.example.aimusicplayer.viewmodel.FlowViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 驾驶模式的ViewModel
 * 负责处理驾驶模式的业务逻辑
 * 使用Flow管理UI状态
 */
@HiltViewModel
class DrivingModeViewModel @Inject constructor(
    application: Application,
    private val musicRepository: MusicRepository,
    private val settingsRepository: SettingsRepository,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    companion object {
        private const val TAG = "DrivingModeViewModel"
    }

    // 是否自动开启语音的StateFlow
    private val _isAutoVoiceEnabledFlow = MutableStateFlow<Boolean>(false)
    val isAutoVoiceEnabledFlow: StateFlow<Boolean> = _isAutoVoiceEnabledFlow.asStateFlow()
    val isAutoVoiceEnabled: LiveData<Boolean> = isAutoVoiceEnabledFlow.asLiveData() // 兼容LiveData

    // 是否正在语音模式的StateFlow
    private val _isVoiceModeActiveFlow = MutableStateFlow<Boolean>(false)
    val isVoiceModeActiveFlow: StateFlow<Boolean> = _isVoiceModeActiveFlow.asStateFlow()
    val isVoiceModeActive: LiveData<Boolean> = isVoiceModeActiveFlow.asLiveData() // 兼容LiveData

    // 推荐歌曲列表的StateFlow
    private val _recommendedSongListFlow = MutableStateFlow<List<Song>>(emptyList())
    val recommendedSongListFlow: StateFlow<List<Song>> = _recommendedSongListFlow.asStateFlow()
    val recommendedSongList: LiveData<List<Song>> = recommendedSongListFlow.asLiveData() // 兼容LiveData

    // 语音识别结果的StateFlow
    private val _voiceRecognitionResultFlow = MutableStateFlow<String>("")
    val voiceRecognitionResultFlow: StateFlow<String> = _voiceRecognitionResultFlow.asStateFlow()
    val voiceRecognitionResult: LiveData<String> = voiceRecognitionResultFlow.asLiveData() // 兼容LiveData

    // 语音命令的StateFlow
    private val _voiceCommandFlow = MutableStateFlow<String>("")
    val voiceCommandFlow: StateFlow<String> = _voiceCommandFlow.asStateFlow()
    val voiceCommand: LiveData<String> = voiceCommandFlow.asLiveData() // 兼容LiveData

    // 语音命令执行结果的StateFlow
    private val _voiceCommandResultFlow = MutableStateFlow<String>("")
    val voiceCommandResultFlow: StateFlow<String> = _voiceCommandResultFlow.asStateFlow()
    val voiceCommandResult: LiveData<String> = voiceCommandResultFlow.asLiveData() // 兼容LiveData

    init {
        // 加载设置
        loadSettings()
    }

    /**
     * 加载设置
     */
    private fun loadSettings() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载设置失败", e)
                handleError(e, "加载设置失败: ${e.message}")
            }
        ) {
            try {
                // 从SettingsRepository加载设置
                val autoVoice = settingsRepository.isAutoVoiceInDrivingEnabled()
                _isAutoVoiceEnabledFlow.value = autoVoice

                // 如果自动开启语音，则激活语音模式
                if (autoVoice) {
                    activateVoiceMode()
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载设置失败", e)
                throw e
            }
        }
    }

    /**
     * 激活语音模式
     */
    fun activateVoiceMode() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "激活语音模式失败", e)
                handleError(e, "激活语音模式失败: ${e.message}")
            }
        ) {
            try {
                // TODO: 激活语音模式
                _isVoiceModeActiveFlow.value = true
            } catch (e: Exception) {
                Log.e(TAG, "激活语音模式失败", e)
                throw e
            }
        }
    }

    /**
     * 停用语音模式
     */
    fun deactivateVoiceMode() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "停用语音模式失败", e)
                handleError(e, "停用语音模式失败: ${e.message}")
            }
        ) {
            try {
                // TODO: 停用语音模式
                _isVoiceModeActiveFlow.value = false
            } catch (e: Exception) {
                Log.e(TAG, "停用语音模式失败", e)
                throw e
            }
        }
    }

    /**
     * 加载推荐歌曲
     */
    fun loadRecommendedSongs() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载推荐歌曲失败", e)
                handleError(e, "加载推荐歌曲失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                // 从MusicRepository加载推荐歌曲
                val result = withContext(Dispatchers.IO) {
                    musicRepository.getRecommendedSongs()
                }

                when (val networkResult = result) {
                    is NetworkResult.Success -> {
                        _recommendedSongListFlow.value = networkResult.data
                    }
                    is NetworkResult.Error -> {
                        handleError(Exception(networkResult.message), networkResult.message)
                    }
                    is NetworkResult.Loading -> {
                        // 加载中状态处理
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 处理语音识别结果
     * @param result 语音识别结果
     */
    fun processVoiceRecognitionResult(result: String) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "处理语音识别结果失败", e)
                handleError(e, "处理语音识别结果失败: ${e.message}")
            }
        ) {
            try {
                _voiceRecognitionResultFlow.value = result

                // 解析语音命令
                val command = parseVoiceCommand(result)
                _voiceCommandFlow.value = command

                // 执行语音命令
                executeVoiceCommand(command)
            } catch (e: Exception) {
                Log.e(TAG, "处理语音识别结果失败", e)
                throw e
            }
        }
    }

    /**
     * 解析语音命令
     * @param voiceResult 语音识别结果
     * @return 解析后的命令
     */
    private fun parseVoiceCommand(voiceResult: String): String {
        // TODO: 语音控制功能待开发
        // 暂时返回未知命令
        return "UNKNOWN"

        /*
        val lowerResult = voiceResult.lowercase()

        return when {
            // 播放控制命令
            lowerResult.contains("播放") || lowerResult.contains("开始") -> "PLAY"
            lowerResult.contains("暂停") || lowerResult.contains("停止") -> "PAUSE"
            lowerResult.contains("下一首") || lowerResult.contains("下一个") || lowerResult.contains("切歌") -> "NEXT"
            lowerResult.contains("上一首") || lowerResult.contains("上一个") || lowerResult.contains("前一首") -> "PREVIOUS"

            // 音量控制命令
            (lowerResult.contains("音量") || lowerResult.contains("声音")) &&
            (lowerResult.contains("大") || lowerResult.contains("高") || lowerResult.contains("增加")) -> "VOLUME_UP"
            (lowerResult.contains("音量") || lowerResult.contains("声音")) &&
            (lowerResult.contains("小") || lowerResult.contains("低") || lowerResult.contains("减少")) -> "VOLUME_DOWN"

            // 播放模式命令
            lowerResult.contains("随机") || lowerResult.contains("乱序") -> "SHUFFLE"
            lowerResult.contains("循环") || lowerResult.contains("重复") -> "REPEAT"

            // 搜索命令
            lowerResult.contains("搜索") || lowerResult.contains("找") -> "SEARCH"
            lowerResult.contains("播放") && (lowerResult.contains("歌曲") || lowerResult.contains("音乐")) -> "PLAY_SONG"

            // 收藏命令
            lowerResult.contains("收藏") || lowerResult.contains("喜欢") -> "LIKE"

            // 导航命令
            lowerResult.contains("返回") || lowerResult.contains("回去") -> "BACK"
            lowerResult.contains("主页") || lowerResult.contains("首页") -> "HOME"

            else -> "UNKNOWN"
        }
        */
    }

    /**
     * 执行语音命令
     * @param command 命令
     */
    private fun executeVoiceCommand(command: String) {
        // TODO: 语音控制功能待开发
        _voiceCommandResultFlow.value = "语音控制功能正在开发中，敬请期待"

        /*
        launchSafely(
            onError = { e ->
                Log.e(TAG, "执行语音命令失败: $command", e)
                handleError(e, "语音命令执行失败: ${e.message}")
            }
        ) {
            when (command) {
                "PLAY" -> {
                    // 播放音乐 - 通过PlayerController控制播放
                    // playerController.play()
                    _voiceCommandResultFlow.value = "开始播放音乐"
                }
                "PAUSE" -> {
                    // 暂停音乐
                    // playerController.pause()
                    _voiceCommandResultFlow.value = "音乐已暂停"
                }
                "NEXT" -> {
                    // 下一首
                    // playerController.next()
                    _voiceCommandResultFlow.value = "切换到下一首"
                }
                "PREVIOUS" -> {
                    // 上一首
                    // playerController.previous()
                    _voiceCommandResultFlow.value = "切换到上一首"
                }
                "VOLUME_UP" -> {
                    // 增大音量 - 通过AudioManager控制
                    try {
                        val audioManager = getApplication<Application>().getSystemService(Context.AUDIO_SERVICE) as AudioManager
                        audioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC, AudioManager.ADJUST_RAISE, AudioManager.FLAG_SHOW_UI)
                        _voiceCommandResultFlow.value = "音量已增大"
                    } catch (e: Exception) {
                        Log.e(TAG, "调节音量失败", e)
                        _voiceCommandResultFlow.value = "音量调节失败"
                    }
                }
                "VOLUME_DOWN" -> {
                    // 减小音量
                    try {
                        val audioManager = getApplication<Application>().getSystemService(Context.AUDIO_SERVICE) as AudioManager
                        audioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC, AudioManager.ADJUST_LOWER, AudioManager.FLAG_SHOW_UI)
                        _voiceCommandResultFlow.value = "音量已减小"
                    } catch (e: Exception) {
                        Log.e(TAG, "调节音量失败", e)
                        _voiceCommandResultFlow.value = "音量调节失败"
                    }
                }
                "SHUFFLE" -> {
                    // 切换随机播放模式
                    // playerController.toggleShuffleMode()
                    _voiceCommandResultFlow.value = "已切换随机播放模式"
                }
                "REPEAT" -> {
                    // 切换循环播放模式
                    // playerController.toggleRepeatMode()
                    _voiceCommandResultFlow.value = "已切换循环播放模式"
                }
                "LIKE" -> {
                    // 收藏当前歌曲
                    // playerController.toggleLike()
                    _voiceCommandResultFlow.value = "已收藏当前歌曲"
                }
                "SEARCH" -> {
                    // 打开搜索界面
                    _voiceCommandResultFlow.value = "请说出要搜索的歌曲名称"
                    // 这里可以触发搜索界面的显示
                }
                "PLAY_SONG" -> {
                    // 播放指定歌曲
                    _voiceCommandResultFlow.value = "请说出要播放的歌曲名称"
                }
                "BACK" -> {
                    // 返回上一页
                    _voiceCommandResultFlow.value = "返回上一页"
                }
                "HOME" -> {
                    // 回到主页
                    _voiceCommandResultFlow.value = "回到主页"
                }
                "UNKNOWN" -> {
                    // 未知命令
                    _voiceCommandResultFlow.value = "抱歉，我没有理解您的指令，请重新说一遍"
                }
                else -> {
                    _voiceCommandResultFlow.value = "不支持的语音命令: $command"
                }
            }
        }
        */
    }
}
