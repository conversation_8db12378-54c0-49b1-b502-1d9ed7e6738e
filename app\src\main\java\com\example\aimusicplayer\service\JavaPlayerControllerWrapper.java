package com.example.aimusicplayer.service;

import androidx.lifecycle.LiveData;
import androidx.media3.common.MediaItem;
import androidx.media3.common.Player;

import com.example.aimusicplayer.data.model.Song;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * PlayerController接口的Java包装类
 * 用于Java代码调用Kotlin接口
 */
@Singleton
public class JavaPlayerControllerWrapper {

    private final PlayerController playerController;

    @Inject
    public JavaPlayerControllerWrapper(PlayerController playerController) {
        this.playerController = playerController;
    }

    /**
     * 播放指定歌曲
     * @param song 要播放的歌曲
     */
    public void play(Song song) {
        // 将Song转换为MediaItem并播放
        // 这里需要实现Song到MediaItem的转换逻辑
        // 暂时使用歌曲ID作为mediaId
        playerController.play(String.valueOf(song.getId()));
    }

    /**
     * 播放指定歌曲列表
     * @param songs 要播放的歌曲列表
     * @param startIndex 起始索引
     */
    public void play(List<Song> songs, int startIndex) {
        // 这个方法需要重新设计，因为PlayerController接口不支持这种调用方式
        // 暂时播放第一首歌
        if (songs != null && !songs.isEmpty() && startIndex >= 0 && startIndex < songs.size()) {
            play(songs.get(startIndex));
        }
    }

    /**
     * 播放/暂停
     */
    public void togglePlayPause() {
        playerController.playPause();
    }

    /**
     * 播放
     */
    public void play() {
        // PlayerController接口没有无参数的play方法
        // 使用playPause方法代替
        playerController.playPause();
    }

    /**
     * 暂停
     */
    public void pause() {
        // PlayerController接口没有pause方法
        // 使用playPause方法代替
        playerController.playPause();
    }

    /**
     * 停止
     */
    public void stop() {
        playerController.stop();
    }

    /**
     * 下一首
     */
    public void next() {
        playerController.next();
    }

    /**
     * 上一首
     */
    public void previous() {
        playerController.prev();
    }

    /**
     * 跳转到指定位置
     * @param position 位置（毫秒）
     */
    public void seekTo(long position) {
        playerController.seekTo((int) position);
    }

    /**
     * 设置播放模式
     * @param mode 播放模式
     */
    public void setPlayMode(int mode) {
        // 将int转换为PlayMode枚举
        PlayMode playMode = PlayMode.values()[mode % PlayMode.values().length];
        playerController.setPlayMode(playMode);
    }

    /**
     * 切换播放模式
     */
    public void togglePlayMode() {
        // PlayerController接口没有togglePlayMode方法
        // 需要手动实现切换逻辑
        PlayMode currentMode = playerController.getPlayMode().getValue();
        if (currentMode != null) {
            PlayMode[] modes = PlayMode.values();
            int currentIndex = currentMode.ordinal();
            int nextIndex = (currentIndex + 1) % modes.length;
            playerController.setPlayMode(modes[nextIndex]);
        }
    }

    /**
     * 获取当前播放状态
     * @return 播放状态LiveData
     */
    public LiveData<PlayState> getPlayState() {
        // PlayerController使用StateFlow，需要转换为LiveData
        // 暂时返回null，需要实现StateFlow到LiveData的转换
        // TODO: 实现StateFlow到LiveData的转换
        return null;
    }

    /**
     * 获取当前播放模式
     * @return 播放模式
     */
    public int getPlayMode() {
        PlayMode mode = playerController.getPlayMode().getValue();
        return mode != null ? mode.ordinal() : 0;
    }

    /**
     * 获取当前播放位置
     * @return 播放位置（毫秒）
     */
    public long getCurrentPosition() {
        return playerController.getCurrentPosition();
    }

    /**
     * 获取当前歌曲时长
     * @return 歌曲时长（毫秒）
     */
    public long getDuration() {
        return playerController.getDuration();
    }

    /**
     * 获取当前播放列表
     * @return 播放列表
     */
    public List<MediaItem> getPlaylist() {
        // 调用重命名后的方法，避免与属性getter冲突
        return playerController.getCurrentPlaylist();
    }

    /**
     * 获取当前播放索引
     * @return 播放索引
     */
    public int getCurrentIndex() {
        return playerController.getCurrentIndex();
    }

    /**
     * 获取播放器实例
     * @return 播放器实例
     */
    public Player getPlayer() {
        // PlayerController接口没有getPlayer方法
        // 返回null或抛出异常
        throw new UnsupportedOperationException("PlayerController接口不支持直接获取Player实例");
    }

    /**
     * 添加歌曲到播放列表
     * @param song 要添加的歌曲
     */
    public void addToPlaylist(Song song) {
        // PlayerController接口没有addToPlaylist方法
        // 需要将Song转换为MediaItem并使用addAndPlay
        // 这里暂时抛出异常，需要在实际使用时实现转换逻辑
        throw new UnsupportedOperationException("需要实现Song到MediaItem的转换逻辑");
    }

    /**
     * 添加歌曲列表到播放列表
     * @param songs 要添加的歌曲列表
     */
    public void addToPlaylist(List<Song> songs) {
        // PlayerController接口没有addToPlaylist方法
        // 需要将Song列表转换为MediaItem列表
        throw new UnsupportedOperationException("需要实现Song列表到MediaItem列表的转换逻辑");
    }

    /**
     * 从播放列表中移除歌曲
     * @param index 要移除的歌曲索引
     */
    public void removeFromPlaylist(int index) {
        // PlayerController接口没有removeFromPlaylist方法
        // 需要通过其他方式实现
        throw new UnsupportedOperationException("PlayerController接口不支持按索引移除歌曲");
    }

    /**
     * 清空播放列表
     */
    public void clearPlaylist() {
        playerController.clearPlaylist();
    }
}
