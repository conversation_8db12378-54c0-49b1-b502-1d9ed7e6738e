package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 轮播图响应
 * 使用MVVM架构
 */
data class BannerResponse(
    @SerializedName("code")
    val code: Int = 0,

    @SerializedName("message")
    val message: String? = null,

    @SerializedName("data")
    val data: Data? = null
) {
    data class Data(
        @SerializedName("banners")
        val banners: List<Banner>? = null
    )


}
